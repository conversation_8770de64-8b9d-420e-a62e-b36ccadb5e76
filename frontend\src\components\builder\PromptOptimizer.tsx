import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { motion } from 'framer-motion';
import { 
  SparklesIcon,
  XMarkIcon,
  LightBulbIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface PromptOptimizerProps {
  prompt: string;
  onOptimize: (optimizedPrompt: string) => void;
  onClose?: () => void;
  onRemove?: () => void;
  canRemove?: boolean;
  isModal?: boolean;
}

const optimizationTypes = [
  {
    id: 'clarity',
    name: 'Clarity',
    description: 'Make the prompt clearer and more specific',
    icon: '🎯'
  },
  {
    id: 'creativity',
    name: 'Creativity',
    description: 'Enhance creative and innovative responses',
    icon: '🎨'
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Optimize for business and professional use',
    icon: '💼'
  },
  {
    id: 'conversational',
    name: 'Conversational',
    description: 'Make responses more natural and engaging',
    icon: '💬'
  }
];

const PromptOptimizer: React.FC<PromptOptimizerProps> = ({
  prompt,
  onOptimize,
  onClose,
  onRemove,
  canRemove = false,
  isModal = false
}) => {
  const [selectedType, setSelectedType] = useState('clarity');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizedPrompt, setOptimizedPrompt] = useState('');
  const [showComparison, setShowComparison] = useState(false);

  const optimizePrompt = async () => {
    setIsOptimizing(true);
    
    // Simulate optimization process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const optimizations = {
      clarity: `You are a professional AI assistant designed to provide clear, accurate, and helpful responses. Your primary goal is to understand user queries thoroughly and deliver precise, well-structured answers.

Key behaviors:
- Always acknowledge the user's question before providing an answer
- Break down complex topics into digestible parts
- Use examples when helpful to illustrate points
- Ask clarifying questions if the user's intent is unclear
- Maintain a helpful and professional tone throughout interactions

${prompt}

Remember to be concise yet comprehensive, ensuring your responses are both informative and easy to understand.`,

      creativity: `You are an innovative and creative AI assistant with a talent for thinking outside the box. Your role is to inspire users with original ideas, creative solutions, and imaginative approaches to their challenges.

Creative guidelines:
- Approach problems from multiple unique angles
- Suggest unconventional but practical solutions
- Use metaphors and analogies to explain concepts
- Encourage brainstorming and exploration of ideas
- Balance creativity with practicality

${prompt}

Let your creativity shine while maintaining helpfulness and relevance to the user's needs.`,

      professional: `You are a highly professional AI business consultant with expertise across multiple industries. Your communication style is formal, authoritative, and results-oriented, designed to support business objectives and professional growth.

Professional standards:
- Maintain a formal and respectful tone at all times
- Provide data-driven insights when possible
- Focus on actionable recommendations
- Consider business implications and ROI
- Use industry-standard terminology appropriately

${prompt}

Ensure all responses meet the highest professional standards and contribute to business success.`,

      conversational: `You are a friendly and engaging AI companion who excels at natural, flowing conversations. Your personality is warm, approachable, and genuinely interested in helping users feel comfortable and understood.

Conversational approach:
- Use a warm, friendly tone that feels natural
- Show genuine interest in the user's concerns
- Use conversational language and avoid overly formal speech
- Share relatable examples and experiences when appropriate
- Maintain engagement through follow-up questions

${prompt}

Focus on creating a comfortable, engaging dialogue that feels like talking with a knowledgeable friend.`
    };

    setOptimizedPrompt(optimizations[selectedType as keyof typeof optimizations]);
    setShowComparison(true);
    setIsOptimizing(false);
  };

  const applyOptimization = () => {
    onOptimize(optimizedPrompt);
    if (onClose) onClose();
  };

  const content = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <SparklesIcon className="h-6 w-6 text-primary-600" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Prompt Optimizer</h3>
          <p className="text-sm text-gray-500">Enhance your prompt for better AI responses</p>
        </div>
      </div>

      {/* Current Prompt */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Current Prompt
        </label>
        <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700 max-h-32 overflow-y-auto">
          {prompt || 'No prompt provided'}
        </div>
      </div>

      {/* Optimization Types */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Optimization Type
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {optimizationTypes.map((type) => (
            <label
              key={type.id}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                selectedType === type.id
                  ? 'border-primary-600 ring-2 ring-primary-600 bg-primary-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input
                type="radio"
                name="optimization-type"
                value={type.id}
                checked={selectedType === type.id}
                onChange={(e) => setSelectedType(e.target.value)}
                className="sr-only"
              />
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">{type.icon}</span>
                  <div className="text-sm">
                    <div className="font-medium text-gray-900">{type.name}</div>
                    <div className="text-gray-500">{type.description}</div>
                  </div>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Optimize Button */}
      {!showComparison && (
        <button
          onClick={optimizePrompt}
          disabled={isOptimizing || !prompt.trim()}
          className="w-full btn-primary disabled:opacity-50"
        >
          {isOptimizing ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="spinner w-4 h-4"></div>
              <span>Optimizing...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <SparklesIcon className="h-5 w-5" />
              <span>Optimize Prompt</span>
            </div>
          )}
        </button>
      )}

      {/* Comparison */}
      {showComparison && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircleIcon className="h-5 w-5" />
            <span className="font-medium">Optimization Complete!</span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Original */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Original Prompt</h4>
              <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700 h-48 overflow-y-auto">
                {prompt}
              </div>
            </div>

            {/* Optimized */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                <span>Optimized Prompt</span>
                <ArrowRightIcon className="h-4 w-4 text-green-600" />
              </h4>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-sm text-gray-700 h-48 overflow-y-auto">
                {optimizedPrompt}
              </div>
            </div>
          </div>

          {/* Improvements */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center space-x-2">
              <LightBulbIcon className="h-5 w-5" />
              <span>Key Improvements</span>
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Added clear role definition and behavioral guidelines</li>
              <li>• Structured the prompt for better AI understanding</li>
              <li>• Included specific instructions for consistent responses</li>
              <li>• Enhanced clarity and reduced ambiguity</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={() => {
                setShowComparison(false);
                setOptimizedPrompt('');
              }}
              className="flex-1 btn-outline"
            >
              Try Different Type
            </button>
            <button
              onClick={applyOptimization}
              className="flex-1 btn-primary"
            >
              Apply Optimization
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );

  if (isModal && onClose) {
    return (
      <Transition appear show={true} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center justify-between mb-6">
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Prompt Optimizer
                    </Dialog.Title>
                    <button
                      onClick={onClose}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                  {content}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <SparklesIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Prompt Optimizer</h3>
            <p className="text-sm text-gray-500">Enhance your prompt for better responses</p>
          </div>
        </div>
        {canRemove && onRemove && (
          <button
            onClick={onRemove}
            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        )}
      </div>
      <div className="p-4">
        {content}
      </div>
    </div>
  );
};

export default PromptOptimizer;
