import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Cog6ToothIcon,
  PlusIcon,
  XMarkIcon,
  TrashIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface ResponseTemplate {
  id: string;
  name: string;
  trigger: string;
  response: string;
  isActive: boolean;
}

interface ResponseTemplatesProps {
  templates: ResponseTemplate[];
  onChange: (templates: ResponseTemplate[]) => void;
  onRemove?: () => void;
  canRemove?: boolean;
}

const ResponseTemplates: React.FC<ResponseTemplatesProps> = ({
  templates,
  onChange,
  onRemove,
  canRemove = false
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ResponseTemplate | null>(null);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    trigger: '',
    response: '',
    isActive: true
  });

  const addTemplate = () => {
    if (!newTemplate.name.trim() || !newTemplate.trigger.trim() || !newTemplate.response.trim()) {
      return;
    }

    const template: ResponseTemplate = {
      id: Date.now().toString(),
      name: newTemplate.name.trim(),
      trigger: newTemplate.trigger.trim(),
      response: newTemplate.response.trim(),
      isActive: newTemplate.isActive
    };

    onChange([...templates, template]);
    setNewTemplate({ name: '', trigger: '', response: '', isActive: true });
    setShowAddForm(false);
  };

  const updateTemplate = (id: string, updates: Partial<ResponseTemplate>) => {
    const updatedTemplates = templates.map(template =>
      template.id === id ? { ...template, ...updates } : template
    );
    onChange(updatedTemplates);
  };

  const deleteTemplate = (id: string) => {
    const updatedTemplates = templates.filter(template => template.id !== id);
    onChange(updatedTemplates);
  };

  const saveEditingTemplate = () => {
    if (editingTemplate) {
      updateTemplate(editingTemplate.id, editingTemplate);
      setEditingTemplate(null);
    }
  };

  const predefinedTemplates = [
    {
      name: 'Greeting',
      trigger: 'hello|hi|hey',
      response: 'Hello! How can I help you today?'
    },
    {
      name: 'Goodbye',
      trigger: 'bye|goodbye|see you',
      response: 'Goodbye! Feel free to come back if you have more questions.'
    },
    {
      name: 'Thank You',
      trigger: 'thank you|thanks',
      response: 'You\'re welcome! I\'m glad I could help.'
    },
    {
      name: 'Help Request',
      trigger: 'help|support|assistance',
      response: 'I\'m here to help! What specific question or task can I assist you with?'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Cog6ToothIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Response Templates</h3>
            <p className="text-sm text-gray-500">Create predefined responses for common queries</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-outline btn-sm"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Template
          </button>
          {canRemove && onRemove && (
            <button
              onClick={onRemove}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      <div className="p-4">
        {/* Existing Templates */}
        <div className="space-y-4 mb-6">
          <AnimatePresence>
            {templates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="border border-gray-200 rounded-lg p-4"
              >
                {editingTemplate?.id === template.id ? (
                  /* Edit Mode */
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={editingTemplate.name}
                      onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
                      placeholder="Template name"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    />
                    <input
                      type="text"
                      value={editingTemplate.trigger}
                      onChange={(e) => setEditingTemplate({ ...editingTemplate, trigger: e.target.value })}
                      placeholder="Trigger keywords (e.g., hello|hi|hey)"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    />
                    <textarea
                      value={editingTemplate.response}
                      onChange={(e) => setEditingTemplate({ ...editingTemplate, response: e.target.value })}
                      placeholder="Response text"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    />
                    <div className="flex items-center justify-between">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editingTemplate.isActive}
                          onChange={(e) => setEditingTemplate({ ...editingTemplate, isActive: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">Active</span>
                      </label>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingTemplate(null)}
                          className="btn-outline btn-sm"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={saveEditingTemplate}
                          className="btn-primary btn-sm"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* View Mode */
                  <div>
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900">{template.name}</h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            template.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {template.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>Triggers:</strong> {template.trigger}
                        </p>
                        <p className="text-sm text-gray-700 bg-gray-50 rounded p-2">
                          {template.response}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1 ml-4">
                        <button
                          onClick={() => setEditingTemplate(template)}
                          className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteTemplate(template.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Add Template Form */}
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border border-gray-200 rounded-lg p-4 mb-6"
          >
            <h4 className="font-medium text-gray-900 mb-3">Add New Template</h4>
            <div className="space-y-3">
              <input
                type="text"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                placeholder="Template name (e.g., Greeting)"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              />
              <input
                type="text"
                value={newTemplate.trigger}
                onChange={(e) => setNewTemplate({ ...newTemplate, trigger: e.target.value })}
                placeholder="Trigger keywords (e.g., hello|hi|hey)"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              />
              <textarea
                value={newTemplate.response}
                onChange={(e) => setNewTemplate({ ...newTemplate, response: e.target.value })}
                placeholder="Response text"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              />
              <div className="flex items-center justify-between">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newTemplate.isActive}
                    onChange={(e) => setNewTemplate({ ...newTemplate, isActive: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">Active</span>
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setNewTemplate({ name: '', trigger: '', response: '', isActive: true });
                    }}
                    className="btn-outline btn-sm"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={addTemplate}
                    className="btn-primary btn-sm"
                  >
                    Add Template
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Predefined Templates */}
        {!showAddForm && templates.length === 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Quick Start Templates</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {predefinedTemplates.map((template, index) => (
                <div
                  key={index}
                  onClick={() => {
                    const newTemplate: ResponseTemplate = {
                      id: Date.now().toString() + index,
                      name: template.name,
                      trigger: template.trigger,
                      response: template.response,
                      isActive: true
                    };
                    onChange([...templates, newTemplate]);
                  }}
                  className="p-3 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 cursor-pointer transition-colors"
                >
                  <h5 className="font-medium text-gray-900 mb-1">{template.name}</h5>
                  <p className="text-xs text-gray-600 mb-2">Triggers: {template.trigger}</p>
                  <p className="text-sm text-gray-700">{template.response}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {templates.length === 0 && !showAddForm && (
          <div className="text-center py-8">
            <Cog6ToothIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates yet</h3>
            <p className="text-gray-600 mb-4">
              Create response templates to handle common queries automatically
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="btn-primary"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Your First Template
            </button>
          </div>
        )}

        {/* Info */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">How templates work:</p>
              <ul className="space-y-1 text-blue-700">
                <li>• Use | to separate multiple trigger keywords</li>
                <li>• Templates are checked before the AI generates a response</li>
                <li>• Matching is case-insensitive and looks for keywords anywhere in the message</li>
                <li>• Templates can help provide consistent responses for common questions</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponseTemplates;
