const redis = require('redis');

let client;

// Create Redis client
function createRedisClient() {
  if (!client) {
    client = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
          console.error('Redis server connection refused');
          return new Error('Redis server connection refused');
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          console.error('Redis retry time exhausted');
          return new Error('Retry time exhausted');
        }
        if (options.attempt > 10) {
          console.error('Redis connection attempts exceeded');
          return undefined;
        }
        // Reconnect after
        return Math.min(options.attempt * 100, 3000);
      }
    });

    client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      console.log('Redis client connected');
    });

    client.on('ready', () => {
      console.log('Redis client ready');
    });

    client.on('end', () => {
      console.log('Redis client disconnected');
    });
  }
  
  return client;
}

// Connect to Redis
async function connectRedis() {
  try {
    const redisClient = createRedisClient();
    await redisClient.connect();
    return redisClient;
  } catch (error) {
    console.error('Failed to connect to Redis:', error);
    throw error;
  }
}

// Get Redis client
function getRedisClient() {
  if (!client || !client.isOpen) {
    throw new Error('Redis client not connected');
  }
  return client;
}

// Disconnect from Redis
async function disconnectRedis() {
  try {
    if (client && client.isOpen) {
      await client.disconnect();
      console.log('Redis connection closed');
    }
  } catch (error) {
    console.error('Error disconnecting from Redis:', error);
    throw error;
  }
}

// Cache operations
const cache = {
  // Set value with expiration
  async set(key, value, expireInSeconds = 3600) {
    try {
      const redisClient = getRedisClient();
      const serializedValue = JSON.stringify(value);
      await redisClient.setEx(key, expireInSeconds, serializedValue);
      return true;
    } catch (error) {
      console.error('Redis SET error:', error);
      return false;
    }
  },

  // Get value
  async get(key) {
    try {
      const redisClient = getRedisClient();
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  },

  // Delete key
  async del(key) {
    try {
      const redisClient = getRedisClient();
      await redisClient.del(key);
      return true;
    } catch (error) {
      console.error('Redis DEL error:', error);
      return false;
    }
  },

  // Check if key exists
  async exists(key) {
    try {
      const redisClient = getRedisClient();
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  },

  // Set expiration
  async expire(key, seconds) {
    try {
      const redisClient = getRedisClient();
      await redisClient.expire(key, seconds);
      return true;
    } catch (error) {
      console.error('Redis EXPIRE error:', error);
      return false;
    }
  },

  // Increment counter
  async incr(key) {
    try {
      const redisClient = getRedisClient();
      return await redisClient.incr(key);
    } catch (error) {
      console.error('Redis INCR error:', error);
      return 0;
    }
  },

  // Hash operations
  async hset(key, field, value) {
    try {
      const redisClient = getRedisClient();
      await redisClient.hSet(key, field, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Redis HSET error:', error);
      return false;
    }
  },

  async hget(key, field) {
    try {
      const redisClient = getRedisClient();
      const value = await redisClient.hGet(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis HGET error:', error);
      return null;
    }
  },

  async hgetall(key) {
    try {
      const redisClient = getRedisClient();
      const hash = await redisClient.hGetAll(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      return result;
    } catch (error) {
      console.error('Redis HGETALL error:', error);
      return {};
    }
  }
};

// Health check for Redis
async function checkRedisHealth() {
  try {
    const redisClient = getRedisClient();
    await redisClient.ping();
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
}

module.exports = {
  createRedisClient,
  connectRedis,
  getRedisClient,
  disconnectRedis,
  cache,
  checkRedisHealth
};
