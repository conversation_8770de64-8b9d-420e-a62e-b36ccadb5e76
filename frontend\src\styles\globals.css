@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-white text-gray-900 antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    body {
      @apply bg-gray-900 text-gray-100;
    }
  }
  
  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Dark mode scrollbar */
  @media (prefers-color-scheme: dark) {
    ::-webkit-scrollbar-track {
      @apply bg-gray-800;
    }
    
    ::-webkit-scrollbar-thumb {
      @apply bg-gray-600;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      @apply bg-gray-500;
    }
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .input-error {
    @apply border-error-300 focus:ring-error-500 focus:border-error-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }
  
  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  /* Layout utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* Typography utilities */
  .heading-1 {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
  }
  
  .heading-2 {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
  }
  
  .heading-3 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight;
  }
  
  .heading-4 {
    @apply text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight;
  }
  
  .body-large {
    @apply text-lg sm:text-xl text-gray-600;
  }
  
  .body-small {
    @apply text-sm text-gray-500;
  }
}

/* Utility styles */
@layer utilities {
  /* Custom animations */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Custom focus ring */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* Backdrop blur utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Text shadow utilities */
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  /* Glassmorphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-glow;
  }
}

/* Third-party component overrides */
/* React Beautiful DnD */
.react-beautiful-dnd-dragging {
  @apply shadow-lg;
}

/* React Select */
.react-select__control {
  @apply border-gray-300 hover:border-gray-400 focus:border-primary-500 focus:ring-1 focus:ring-primary-500;
}

.react-select__menu {
  @apply border border-gray-200 shadow-lg;
}

.react-select__option--is-focused {
  @apply bg-primary-50 text-primary-900;
}

.react-select__option--is-selected {
  @apply bg-primary-600 text-white;
}

/* Recharts customization */
.recharts-tooltip-wrapper {
  @apply shadow-lg border border-gray-200 rounded-lg;
}

/* Code syntax highlighting */
.code-block {
  @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
}

.code-block pre {
  @apply m-0;
}

.code-block code {
  @apply text-sm font-mono;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply shadow-none border border-gray-300;
  }
}
