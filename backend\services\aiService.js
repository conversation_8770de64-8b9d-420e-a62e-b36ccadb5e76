const OpenAI = require('openai');
const Anthropic = require('anthropic');

// Initialize AI clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  organization: process.env.OPENAI_ORG_ID,
});

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

// Pricing per 1K tokens (approximate)
const PRICING = {
  OPENAI: {
    'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    'gpt-4': { input: 0.03, output: 0.06 },
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
  },
  ANTHROPIC: {
    'claude-3-haiku': { input: 0.00025, output: 0.00125 },
    'claude-3-sonnet': { input: 0.003, output: 0.015 },
    'claude-3-opus': { input: 0.015, output: 0.075 },
  }
};

// Generate response using OpenAI
const generateOpenAIResponse = async ({
  prompt,
  input,
  personality = {},
  model = 'gpt-3.5-turbo',
  temperature = 0.7,
  maxTokens = 1000,
  context = []
}) => {
  try {
    // Build system message with personality
    let systemMessage = prompt;
    
    if (personality.traits && personality.traits.length > 0) {
      systemMessage += `\n\nPersonality traits: ${personality.traits.join(', ')}`;
    }
    
    if (personality.tone) {
      systemMessage += `\nTone: ${personality.tone}`;
    }
    
    if (personality.style) {
      systemMessage += `\nCommunication style: ${personality.style}`;
    }

    // Build messages array
    const messages = [
      { role: 'system', content: systemMessage }
    ];

    // Add context if provided
    if (context && context.length > 0) {
      context.forEach(msg => {
        messages.push({
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.content
        });
      });
    }

    // Add current user input
    messages.push({ role: 'user', content: input });

    const response = await openai.chat.completions.create({
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream: false,
    });

    const completion = response.choices[0];
    const usage = response.usage;

    // Calculate cost
    const pricing = PRICING.OPENAI[model] || PRICING.OPENAI['gpt-3.5-turbo'];
    const cost = (usage.prompt_tokens / 1000) * pricing.input + 
                 (usage.completion_tokens / 1000) * pricing.output;

    return {
      text: completion.message.content,
      tokens: usage.total_tokens,
      inputTokens: usage.prompt_tokens,
      outputTokens: usage.completion_tokens,
      cost: parseFloat(cost.toFixed(6)),
      model,
      provider: 'OPENAI'
    };
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
};

// Generate response using Anthropic
const generateAnthropicResponse = async ({
  prompt,
  input,
  personality = {},
  model = 'claude-3-haiku-20240307',
  temperature = 0.7,
  maxTokens = 1000,
  context = []
}) => {
  try {
    // Build system message with personality
    let systemMessage = prompt;
    
    if (personality.traits && personality.traits.length > 0) {
      systemMessage += `\n\nPersonality traits: ${personality.traits.join(', ')}`;
    }
    
    if (personality.tone) {
      systemMessage += `\nTone: ${personality.tone}`;
    }
    
    if (personality.style) {
      systemMessage += `\nCommunication style: ${personality.style}`;
    }

    // Build messages array
    const messages = [];

    // Add context if provided
    if (context && context.length > 0) {
      context.forEach(msg => {
        messages.push({
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.content
        });
      });
    }

    // Add current user input
    messages.push({ role: 'user', content: input });

    const response = await anthropic.messages.create({
      model,
      system: systemMessage,
      messages,
      temperature,
      max_tokens: maxTokens,
    });

    // Calculate approximate cost (Anthropic doesn't provide exact token counts)
    const estimatedInputTokens = Math.ceil((systemMessage.length + input.length) / 4);
    const estimatedOutputTokens = Math.ceil(response.content[0].text.length / 4);
    const totalTokens = estimatedInputTokens + estimatedOutputTokens;

    const pricing = PRICING.ANTHROPIC[model] || PRICING.ANTHROPIC['claude-3-haiku'];
    const cost = (estimatedInputTokens / 1000) * pricing.input + 
                 (estimatedOutputTokens / 1000) * pricing.output;

    return {
      text: response.content[0].text,
      tokens: totalTokens,
      inputTokens: estimatedInputTokens,
      outputTokens: estimatedOutputTokens,
      cost: parseFloat(cost.toFixed(6)),
      model,
      provider: 'ANTHROPIC'
    };
  } catch (error) {
    console.error('Anthropic API error:', error);
    throw new Error(`Anthropic API error: ${error.message}`);
  }
};

// Main function to generate response
const generateResponse = async (options) => {
  const { provider = 'OPENAI' } = options;

  switch (provider) {
    case 'OPENAI':
      return await generateOpenAIResponse(options);
    case 'ANTHROPIC':
      return await generateAnthropicResponse(options);
    default:
      throw new Error(`Unsupported AI provider: ${provider}`);
  }
};

// Optimize prompt for better responses
const optimizePrompt = (originalPrompt, optimizationType = 'clarity') => {
  const optimizations = {
    clarity: {
      prefix: 'You are a helpful AI assistant. Please provide clear, concise, and accurate responses. ',
      suffix: '\n\nAlways structure your responses in a clear and organized manner.'
    },
    creativity: {
      prefix: 'You are a creative AI assistant. Think outside the box and provide innovative responses. ',
      suffix: '\n\nFeel free to be creative and engaging in your responses while maintaining accuracy.'
    },
    professional: {
      prefix: 'You are a professional AI assistant. Maintain a formal and business-appropriate tone. ',
      suffix: '\n\nEnsure all responses are professional, well-structured, and suitable for business contexts.'
    },
    friendly: {
      prefix: 'You are a friendly and approachable AI assistant. Be warm and conversational. ',
      suffix: '\n\nMaintain a friendly, helpful tone while providing accurate information.'
    }
  };

  const optimization = optimizations[optimizationType] || optimizations.clarity;
  return optimization.prefix + originalPrompt + optimization.suffix;
};

// Validate prompt quality
const validatePrompt = (prompt) => {
  const issues = [];
  
  if (prompt.length < 10) {
    issues.push('Prompt is too short. Consider adding more context.');
  }
  
  if (prompt.length > 8000) {
    issues.push('Prompt is very long. Consider making it more concise.');
  }
  
  if (!prompt.includes('You are') && !prompt.includes('Act as')) {
    issues.push('Consider starting with role definition (e.g., "You are..." or "Act as...")');
  }
  
  const questionWords = ['what', 'how', 'why', 'when', 'where', 'who'];
  const hasQuestions = questionWords.some(word => 
    prompt.toLowerCase().includes(word)
  );
  
  if (!hasQuestions && !prompt.includes('?')) {
    issues.push('Consider adding specific instructions or questions to guide responses.');
  }

  return {
    isValid: issues.length === 0,
    issues,
    score: Math.max(0, 100 - (issues.length * 20))
  };
};

// Get available models for each provider
const getAvailableModels = () => {
  return {
    OPENAI: [
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and efficient for most tasks' },
      { id: 'gpt-4', name: 'GPT-4', description: 'Most capable model for complex tasks' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Latest GPT-4 with improved performance' },
    ],
    ANTHROPIC: [
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fast and cost-effective' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance and speed' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful for complex reasoning' },
    ]
  };
};

// Test AI service connection
const testConnection = async (provider = 'OPENAI') => {
  try {
    const testResponse = await generateResponse({
      provider,
      prompt: 'You are a test assistant.',
      input: 'Say "Connection successful" if you can read this.',
      model: provider === 'OPENAI' ? 'gpt-3.5-turbo' : 'claude-3-haiku-20240307',
      temperature: 0.1,
      maxTokens: 50
    });

    return {
      success: true,
      provider,
      response: testResponse.text,
      latency: Date.now()
    };
  } catch (error) {
    return {
      success: false,
      provider,
      error: error.message
    };
  }
};

// Stream response (for real-time chat)
const streamResponse = async (options, onChunk) => {
  const { provider = 'OPENAI' } = options;

  if (provider !== 'OPENAI') {
    throw new Error('Streaming is currently only supported for OpenAI');
  }

  try {
    const { prompt, input, personality = {}, model = 'gpt-3.5-turbo', temperature = 0.7, maxTokens = 1000, context = [] } = options;

    // Build system message
    let systemMessage = prompt;
    if (personality.traits) systemMessage += `\n\nPersonality traits: ${personality.traits.join(', ')}`;
    if (personality.tone) systemMessage += `\nTone: ${personality.tone}`;
    if (personality.style) systemMessage += `\nCommunication style: ${personality.style}`;

    const messages = [
      { role: 'system', content: systemMessage },
      ...context.map(msg => ({ role: msg.role, content: msg.content })),
      { role: 'user', content: input }
    ];

    const stream = await openai.chat.completions.create({
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream: true,
    });

    let fullText = '';
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullText += content;
        onChunk(content, fullText);
      }
    }

    return { text: fullText, provider: 'OPENAI', model };
  } catch (error) {
    console.error('Streaming error:', error);
    throw error;
  }
};

module.exports = {
  generateResponse,
  optimizePrompt,
  validatePrompt,
  getAvailableModels,
  testConnection,
  streamResponse
};
