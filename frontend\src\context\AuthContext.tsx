import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import { api } from '@/utils/api';
import toast from 'react-hot-toast';

// Types
interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'DELETED';
  subscriptionStatus: 'FREE' | 'ACTIVE' | 'PAST_DUE' | 'CANCELED' | 'UNPAID';
  emailVerified: boolean;
  brandName?: string;
  brandLogo?: string;
  brandColor?: string;
  customDomain?: string;
  createdAt: string;
  lastLoginAt?: string;
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (role: string | string[]) => boolean;
  hasSubscription: (plan: string | string[]) => boolean;
}

interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Token management
const TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';

const getStoredTokens = (): AuthTokens | null => {
  const accessToken = Cookies.get(TOKEN_KEY);
  const refreshToken = Cookies.get(REFRESH_TOKEN_KEY);
  
  if (accessToken && refreshToken) {
    return { accessToken, refreshToken };
  }
  
  return null;
};

const setStoredTokens = (tokens: AuthTokens) => {
  const cookieOptions = {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
  };
  
  Cookies.set(TOKEN_KEY, tokens.accessToken, {
    ...cookieOptions,
    expires: 1, // 1 day
  });
  
  Cookies.set(REFRESH_TOKEN_KEY, tokens.refreshToken, {
    ...cookieOptions,
    expires: 7, // 7 days
  });
};

const clearStoredTokens = () => {
  Cookies.remove(TOKEN_KEY);
  Cookies.remove(REFRESH_TOKEN_KEY);
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const tokens = getStoredTokens();
      
      if (!tokens) {
        setLoading(false);
        return;
      }

      // Set the token for API requests
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.accessToken}`;

      // Try to get user profile
      const response = await api.get('/auth/profile');
      setUser(response.data.user);
    } catch (error: any) {
      // If token is invalid, try to refresh
      if (error.response?.status === 401) {
        try {
          await refreshTokens();
        } catch (refreshError) {
          clearStoredTokens();
          delete api.defaults.headers.common['Authorization'];
        }
      } else {
        console.error('Auth initialization error:', error);
        clearStoredTokens();
        delete api.defaults.headers.common['Authorization'];
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      const response = await api.post('/auth/login', {
        email,
        password,
        rememberMe,
      });

      const { user: userData, tokens } = response.data;
      
      setUser(userData);
      setStoredTokens(tokens);
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.accessToken}`;
      
      toast.success('Login successful!');
      
      // Redirect to dashboard or intended page
      const redirectTo = router.query.redirect as string || '/dashboard';
      router.push(redirectTo);
    } catch (error: any) {
      const message = error.response?.data?.error || 'Login failed';
      toast.error(message);
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await api.post('/auth/register', data);
      
      const { user: userData, tokens } = response.data;
      
      setUser(userData);
      setStoredTokens(tokens);
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.accessToken}`;
      
      toast.success('Registration successful! Please check your email to verify your account.');
      router.push('/dashboard');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Registration failed';
      toast.error(message);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const tokens = getStoredTokens();
      
      if (tokens) {
        await api.post('/auth/logout', {
          refreshToken: tokens.refreshToken,
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      clearStoredTokens();
      delete api.defaults.headers.common['Authorization'];
      toast.success('Logged out successfully');
      router.push('/');
    }
  };

  const refreshTokens = async () => {
    const tokens = getStoredTokens();
    
    if (!tokens) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await api.post('/auth/refresh', {
        refreshToken: tokens.refreshToken,
      });

      const newTokens = response.data.tokens;
      setStoredTokens(newTokens);
      api.defaults.headers.common['Authorization'] = `Bearer ${newTokens.accessToken}`;
      
      return newTokens;
    } catch (error) {
      clearStoredTokens();
      delete api.defaults.headers.common['Authorization'];
      setUser(null);
      throw error;
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      const response = await api.put('/auth/profile', data);
      setUser(response.data.user);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Profile update failed';
      toast.error(message);
      throw error;
    }
  };

  const requestPasswordReset = async (email: string) => {
    try {
      await api.post('/auth/request-password-reset', { email });
      toast.success('Password reset email sent');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Password reset request failed';
      toast.error(message);
      throw error;
    }
  };

  const resetPassword = async (token: string, password: string) => {
    try {
      await api.post('/auth/reset-password', { token, password });
      toast.success('Password reset successful');
      router.push('/auth/login');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Password reset failed';
      toast.error(message);
      throw error;
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      await api.get(`/auth/verify-email/${token}`);
      
      // Refresh user data
      if (user) {
        const response = await api.get('/auth/profile');
        setUser(response.data.user);
      }
      
      toast.success('Email verified successfully');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Email verification failed';
      toast.error(message);
      throw error;
    }
  };

  // Utility functions
  const isAuthenticated = !!user;

  const hasRole = (roles: string | string[]): boolean => {
    if (!user) return false;
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  };

  const hasSubscription = (plans: string | string[]): boolean => {
    if (!user) return false;
    const planArray = Array.isArray(plans) ? plans : [plans];
    return planArray.includes(user.subscriptionStatus);
  };

  // Set up axios interceptor for token refresh
  useEffect(() => {
    const interceptor = api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await refreshTokens();
            return api(originalRequest);
          } catch (refreshError) {
            // Refresh failed, redirect to login
            router.push('/auth/login');
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.response.eject(interceptor);
    };
  }, [router]);

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    refreshToken: refreshTokens,
    updateProfile,
    requestPasswordReset,
    resetPassword,
    verifyEmail,
    isAuthenticated,
    hasRole,
    hasSubscription,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
