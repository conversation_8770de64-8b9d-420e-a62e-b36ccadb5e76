import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  RocketLaunchIcon, 
  CurrencyDollarIcon, 
  CogIcon, 
  ChartBarIcon,
  SparklesIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import Layout from '@/components/common/Layout';

const features = [
  {
    icon: CogIcon,
    title: 'AI Agent Builder',
    description: 'Intuitive drag-and-drop interface for creating AI agents with custom prompts and personality settings.',
  },
  {
    icon: CurrencyDollarIcon,
    title: 'Monetization System',
    description: 'Built-in subscription management, one-time payments, and revenue sharing with configurable pricing tiers.',
  },
  {
    icon: RocketLaunchIcon,
    title: 'One-Click Deployment',
    description: 'Deploy as embeddable widgets, standalone web apps, or API endpoints with just one click.',
  },
  {
    icon: ChartBarIcon,
    title: 'Analytics Dashboard',
    description: 'Real-time metrics for usage, revenue, customer engagement, and performance optimization.',
  },
  {
    icon: SparklesIcon,
    title: 'White-label Branding',
    description: 'Custom domains, logos, colors, and complete brand customization for deployed agents.',
  },
  {
    icon: CurrencyDollarIcon,
    title: 'Payment Integration',
    description: 'Stripe and PayPal integration with automated billing, invoicing, and payout management.',
  },
];

const stats = [
  { label: 'Active Users', value: '10,000+' },
  { label: 'AI Agents Created', value: '50,000+' },
  { label: 'Revenue Generated', value: '$2M+' },
  { label: 'Countries Served', value: '150+' },
];

export default function HomePage() {
  return (
    <>
      <Head>
        <title>PromptCash - Monetize Your AI Interactions</title>
        <meta 
          name="description" 
          content="Build, deploy, and monetize AI agents from custom prompts with our comprehensive platform. Start earning from your AI creations today." 
        />
        <meta name="keywords" content="AI agents, monetization, chatbots, prompts, SaaS, platform" />
        <meta property="og:title" content="PromptCash - Monetize Your AI Interactions" />
        <meta property="og:description" content="Build, deploy, and monetize AI agents from custom prompts" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://promptcash.com" />
      </Head>

      <Layout>
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 to-primary-100 py-20 sm:py-32">
          <div className="container-custom">
            <div className="mx-auto max-w-4xl text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="heading-1 text-gray-900 mb-6">
                  Monetize Your{' '}
                  <span className="text-gradient-primary">AI Interactions</span>
                </h1>
                <p className="body-large mb-8 max-w-3xl mx-auto">
                  Build, deploy, and monetize AI agents from custom prompts with our comprehensive platform. 
                  Turn your AI expertise into a profitable business with just a few clicks.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/auth/register" className="btn-primary btn-lg">
                    Start Building Free
                    <ArrowRightIcon className="ml-2 h-5 w-5" />
                  </Link>
                  <Link href="/demo" className="btn-outline btn-lg">
                    View Demo
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Background decoration */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-200 rounded-full opacity-20 blur-3xl"></div>
            <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-96 h-96 bg-primary-300 rounded-full opacity-20 blur-3xl"></div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="container-custom">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-3xl sm:text-4xl font-bold text-primary-600 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="section-padding bg-gray-50">
          <div className="container-custom">
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="heading-2 text-gray-900 mb-4">
                  Everything You Need to Succeed
                </h2>
                <p className="body-large max-w-3xl mx-auto">
                  Our comprehensive platform provides all the tools you need to build, deploy, 
                  and monetize AI agents without any technical complexity.
                </p>
              </motion.div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="card hover-lift"
                >
                  <div className="card-body">
                    <div className="flex items-center mb-4">
                      <div className="flex-shrink-0">
                        <feature.icon className="h-8 w-8 text-primary-600" />
                      </div>
                      <h3 className="ml-3 text-xl font-semibold text-gray-900">
                        {feature.title}
                      </h3>
                    </div>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="section-padding bg-primary-600">
          <div className="container-custom">
            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="heading-2 text-white mb-4">
                  Ready to Start Earning?
                </h2>
                <p className="body-large text-primary-100 mb-8 max-w-2xl mx-auto">
                  Join thousands of creators who are already monetizing their AI agents. 
                  Start your journey today with our free plan.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/auth/register" className="btn bg-white text-primary-600 hover:bg-gray-50 btn-lg">
                    Get Started Free
                  </Link>
                  <Link href="/pricing" className="btn border-white text-white hover:bg-white hover:text-primary-600 btn-lg">
                    View Pricing
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}
