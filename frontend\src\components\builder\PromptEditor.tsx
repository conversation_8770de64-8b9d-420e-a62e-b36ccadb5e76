import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChatBubbleLeftRightIcon,
  SparklesIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface PromptEditorProps {
  prompt: string;
  onChange: (value: string) => void;
  onOptimize?: () => void;
  onRemove?: () => void;
  canRemove?: boolean;
}

interface PromptSuggestion {
  id: string;
  title: string;
  description: string;
  template: string;
  category: 'assistant' | 'creative' | 'business' | 'educational';
}

const promptSuggestions: PromptSuggestion[] = [
  {
    id: 'helpful-assistant',
    title: 'Helpful Assistant',
    description: 'A general-purpose helpful AI assistant',
    template: 'You are a helpful AI assistant. Provide clear, accurate, and useful responses to user questions. Be friendly and professional in your communication.',
    category: 'assistant'
  },
  {
    id: 'creative-writer',
    title: 'Creative Writer',
    description: 'An AI that helps with creative writing tasks',
    template: 'You are a creative writing assistant. Help users with storytelling, character development, plot ideas, and creative writing techniques. Be imaginative and inspiring.',
    category: 'creative'
  },
  {
    id: 'business-advisor',
    title: 'Business Advisor',
    description: 'An AI focused on business and entrepreneurship',
    template: 'You are a business advisor with expertise in entrepreneurship, strategy, and business development. Provide practical, actionable advice for business challenges.',
    category: 'business'
  },
  {
    id: 'tutor',
    title: 'Educational Tutor',
    description: 'An AI tutor for learning and education',
    template: 'You are an educational tutor. Help students understand concepts, provide explanations, and guide learning. Be patient, encouraging, and adapt to different learning styles.',
    category: 'educational'
  }
];

const PromptEditor: React.FC<PromptEditorProps> = ({
  prompt,
  onChange,
  onOptimize,
  onRemove,
  canRemove = false
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [promptAnalysis, setPromptAnalysis] = useState<any>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [prompt]);

  // Analyze prompt quality
  useEffect(() => {
    if (prompt.length > 0) {
      analyzePrompt(prompt);
    } else {
      setPromptAnalysis(null);
    }
  }, [prompt]);

  const analyzePrompt = (text: string) => {
    const analysis = {
      length: text.length,
      wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
      hasRoleDefinition: /you are|act as|you're a|you're an/i.test(text),
      hasInstructions: /please|should|must|always|never/i.test(text),
      hasContext: /context|background|situation/i.test(text),
      score: 0,
      issues: [] as string[],
      suggestions: [] as string[]
    };

    // Calculate score and provide feedback
    if (analysis.length < 20) {
      analysis.issues.push('Prompt is too short');
    } else if (analysis.length > 2000) {
      analysis.issues.push('Prompt might be too long');
    } else {
      analysis.score += 20;
    }

    if (analysis.hasRoleDefinition) {
      analysis.score += 25;
    } else {
      analysis.issues.push('Consider defining the AI\'s role');
      analysis.suggestions.push('Start with "You are..." or "Act as..."');
    }

    if (analysis.hasInstructions) {
      analysis.score += 25;
    } else {
      analysis.suggestions.push('Add specific instructions for behavior');
    }

    if (analysis.hasContext) {
      analysis.score += 15;
    } else {
      analysis.suggestions.push('Provide context for better responses');
    }

    if (analysis.wordCount >= 10) {
      analysis.score += 15;
    }

    setPromptAnalysis(analysis);
  };

  const handleSuggestionSelect = (suggestion: PromptSuggestion) => {
    onChange(suggestion.template);
    setShowSuggestions(false);
  };

  const filteredSuggestions = selectedCategory === 'all' 
    ? promptSuggestions 
    : promptSuggestions.filter(s => s.category === selectedCategory);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Prompt Configuration</h3>
            <p className="text-sm text-gray-500">Define how your AI agent should behave</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {onOptimize && (
            <button
              onClick={onOptimize}
              className="btn-outline btn-sm"
            >
              <SparklesIcon className="h-4 w-4 mr-2" />
              Optimize
            </button>
          )}
          {canRemove && onRemove && (
            <button
              onClick={onRemove}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Prompt textarea */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            System Prompt *
          </label>
          <textarea
            ref={textareaRef}
            value={prompt}
            onChange={(e) => onChange(e.target.value)}
            placeholder="You are a helpful AI assistant. Provide clear, accurate, and useful responses..."
            className="w-full min-h-[120px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
            style={{ minHeight: '120px' }}
          />
          <div className="flex items-center justify-between mt-2 text-sm text-gray-500">
            <span>{prompt.length} characters</span>
            <button
              onClick={() => setShowSuggestions(!showSuggestions)}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              {showSuggestions ? 'Hide' : 'Show'} suggestions
            </button>
          </div>
        </div>

        {/* Prompt analysis */}
        {promptAnalysis && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-3 rounded-lg ${getScoreBgColor(promptAnalysis.score)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                {promptAnalysis.score >= 80 ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                ) : promptAnalysis.score >= 60 ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                ) : (
                  <InformationCircleIcon className="h-5 w-5 text-red-600" />
                )}
                <span className="font-medium text-gray-900">Prompt Quality</span>
              </div>
              <span className={`font-bold ${getScoreColor(promptAnalysis.score)}`}>
                {promptAnalysis.score}/100
              </span>
            </div>
            
            {promptAnalysis.issues.length > 0 && (
              <div className="mb-2">
                <p className="text-sm font-medium text-gray-700 mb-1">Issues:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  {promptAnalysis.issues.map((issue, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-red-500 mt-0.5">•</span>
                      <span>{issue}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {promptAnalysis.suggestions.length > 0 && (
              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">Suggestions:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  {promptAnalysis.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </motion.div>
        )}

        {/* Prompt suggestions */}
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border border-gray-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-900">Prompt Templates</h4>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="all">All Categories</option>
                <option value="assistant">Assistant</option>
                <option value="creative">Creative</option>
                <option value="business">Business</option>
                <option value="educational">Educational</option>
              </select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {filteredSuggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  onClick={() => handleSuggestionSelect(suggestion)}
                  className="p-3 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 cursor-pointer transition-colors"
                >
                  <h5 className="font-medium text-gray-900 mb-1">{suggestion.title}</h5>
                  <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                  <p className="text-xs text-gray-500 line-clamp-2">{suggestion.template}</p>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Tips for better prompts:</p>
              <ul className="space-y-1 text-blue-700">
                <li>• Start with a clear role definition</li>
                <li>• Provide specific instructions and examples</li>
                <li>• Include context about the expected audience</li>
                <li>• Specify the desired tone and style</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptEditor;
