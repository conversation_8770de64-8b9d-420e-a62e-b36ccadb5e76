{"name": "promptcash-backend", "version": "1.0.0", "description": "PromptCash Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js'", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "migrate": "npx prisma migrate dev", "migrate:prod": "npx prisma migrate deploy", "seed": "npx prisma db seed", "generate": "npx prisma generate", "studio": "npx prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "stripe": "^14.9.0", "paypal-rest-sdk": "^1.8.1", "@sendgrid/mail": "^8.1.0", "openai": "^4.20.1", "anthropic": "^0.9.1", "aws-sdk": "^2.1509.0", "uuid": "^9.0.1", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "crypto": "^1.0.1", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "prisma": "^5.7.1", "@types/jest": "^29.5.8"}, "prisma": {"seed": "node prisma/seed.js"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api", "backend", "ai", "agents", "monetization"], "author": "HectorTa1989", "license": "MIT"}