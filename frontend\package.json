{"name": "promptcash-frontend", "version": "1.0.0", "description": "PromptCash Frontend - Next.js Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest --watch", "test:ci": "jest --ci --coverage", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "react-beautiful-dnd": "^13.1.1", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "socket.io-client": "^4.7.4", "stripe": "^14.9.0", "axios": "^1.6.2", "js-cookie": "^3.0.5", "date-fns": "^2.30.0", "uuid": "^9.0.1", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "react-select": "^5.8.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "prismjs": "^1.29.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/js-cookie": "^3.0.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.9", "@types/react-copy-to-clipboard": "^5.0.7", "typescript": "^5.3.3", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4"}, "engines": {"node": ">=18.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}