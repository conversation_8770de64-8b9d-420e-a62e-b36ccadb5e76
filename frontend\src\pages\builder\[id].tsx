import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { 
  ArrowLeftIcon, 
  PlayIcon, 
  EyeIcon, 
  Cog6ToothIcon,
  DocumentDuplicateIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Layout from '@/components/common/Layout';
import { useAuth } from '@/context/AuthContext';
import { agentsAPI } from '@/utils/api';
import AgentBuilder from '@/components/builder/AgentBuilder';
import AgentPreview from '@/components/builder/AgentPreview';
import AgentSettings from '@/components/builder/AgentSettings';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ConfirmDialog from '@/components/common/ConfirmDialog';

const AgentBuilderPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [activeTab, setActiveTab] = useState<'builder' | 'preview' | 'settings'>('builder');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=' + encodeURIComponent(router.asPath));
    }
  }, [isAuthenticated, router]);

  // Fetch agent data
  const { data: agentData, isLoading, error } = useQuery(
    ['agent', id],
    () => agentsAPI.getById(id as string),
    {
      enabled: !!id && id !== 'new',
      retry: 1,
      onError: (error: any) => {
        if (error.response?.status === 404) {
          toast.error('Agent not found');
          router.push('/dashboard/agents');
        } else {
          toast.error('Failed to load agent');
        }
      }
    }
  );

  // Update agent mutation
  const updateAgentMutation = useMutation(
    (data: any) => agentsAPI.update(id as string, data),
    {
      onSuccess: () => {
        toast.success('Agent updated successfully');
        queryClient.invalidateQueries(['agent', id]);
        queryClient.invalidateQueries(['agents']);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update agent');
      },
      onSettled: () => {
        setIsSaving(false);
      }
    }
  );

  // Create agent mutation (for new agents)
  const createAgentMutation = useMutation(
    (data: any) => agentsAPI.create(data),
    {
      onSuccess: (response) => {
        toast.success('Agent created successfully');
        router.push(`/builder/${response.data.agent.id}`);
        queryClient.invalidateQueries(['agents']);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to create agent');
      },
      onSettled: () => {
        setIsSaving(false);
      }
    }
  );

  // Delete agent mutation
  const deleteAgentMutation = useMutation(
    () => agentsAPI.delete(id as string),
    {
      onSuccess: () => {
        toast.success('Agent deleted successfully');
        router.push('/dashboard/agents');
        queryClient.invalidateQueries(['agents']);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to delete agent');
      }
    }
  );

  // Clone agent mutation
  const cloneAgentMutation = useMutation(
    (name: string) => agentsAPI.clone(id as string, { name }),
    {
      onSuccess: (response) => {
        toast.success('Agent cloned successfully');
        router.push(`/builder/${response.data.agent.id}`);
        queryClient.invalidateQueries(['agents']);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to clone agent');
      }
    }
  );

  const handleSave = async (agentData: any) => {
    setIsSaving(true);
    
    if (id === 'new') {
      createAgentMutation.mutate(agentData);
    } else {
      updateAgentMutation.mutate(agentData);
    }
  };

  const handleDelete = () => {
    setShowDeleteDialog(false);
    deleteAgentMutation.mutate();
  };

  const handleClone = () => {
    const agent = agentData?.data?.agent;
    if (agent) {
      const cloneName = `${agent.name} (Copy)`;
      cloneAgentMutation.mutate(cloneName);
    }
  };

  const isNewAgent = id === 'new';
  const agent = agentData?.data?.agent;

  if (!isAuthenticated) {
    return <LoadingSpinner />;
  }

  if (isLoading && !isNewAgent) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (error && !isNewAgent) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Agent Not Found</h2>
            <p className="text-gray-600 mb-6">The agent you're looking for doesn't exist or you don't have access to it.</p>
            <button
              onClick={() => router.push('/dashboard/agents')}
              className="btn-primary"
            >
              Back to Agents
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  const tabs = [
    { id: 'builder', name: 'Builder', icon: Cog6ToothIcon },
    { id: 'preview', name: 'Preview', icon: EyeIcon },
    { id: 'settings', name: 'Settings', icon: Cog6ToothIcon },
  ];

  return (
    <>
      <Head>
        <title>
          {isNewAgent ? 'Create New Agent' : `Edit ${agent?.name}`} - PromptCash
        </title>
        <meta name="description" content="Build and configure your AI agent with our intuitive drag-and-drop interface" />
      </Head>

      <Layout showHeader={false}>
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
          <div className="container-custom">
            <div className="flex items-center justify-between h-16">
              {/* Left side */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard/agents')}
                  className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </button>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    {isNewAgent ? 'Create New Agent' : agent?.name}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {isNewAgent ? 'Build your AI agent from scratch' : 'Edit and configure your agent'}
                  </p>
                </div>
              </div>

              {/* Right side */}
              <div className="flex items-center space-x-3">
                {!isNewAgent && (
                  <>
                    <button
                      onClick={handleClone}
                      disabled={cloneAgentMutation.isLoading}
                      className="btn-outline btn-sm"
                    >
                      <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                      Clone
                    </button>
                    <button
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={deleteAgentMutation.isLoading}
                      className="btn-danger btn-sm"
                    >
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete
                    </button>
                  </>
                )}
                <button
                  onClick={() => {/* Handle save */}}
                  disabled={isSaving}
                  className="btn-primary btn-sm"
                >
                  {isSaving ? (
                    <div className="spinner w-4 h-4 mr-2" />
                  ) : (
                    <PlayIcon className="h-4 w-4 mr-2" />
                  )}
                  {isNewAgent ? 'Create Agent' : 'Save Changes'}
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span className="font-medium">{tab.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1">
          {activeTab === 'builder' && (
            <AgentBuilder
              agent={agent}
              onSave={handleSave}
              isLoading={isSaving}
              isNewAgent={isNewAgent}
            />
          )}
          {activeTab === 'preview' && (
            <AgentPreview
              agent={agent}
              isNewAgent={isNewAgent}
            />
          )}
          {activeTab === 'settings' && (
            <AgentSettings
              agent={agent}
              onSave={handleSave}
              isLoading={isSaving}
              isNewAgent={isNewAgent}
            />
          )}
        </div>

        {/* Delete confirmation dialog */}
        <ConfirmDialog
          isOpen={showDeleteDialog}
          onClose={() => setShowDeleteDialog(false)}
          onConfirm={handleDelete}
          title="Delete Agent"
          message={`Are you sure you want to delete "${agent?.name}"? This action cannot be undone.`}
          confirmText="Delete"
          confirmButtonClass="btn-danger"
          isLoading={deleteAgentMutation.isLoading}
        />
      </Layout>
    </>
  );
};

export default AgentBuilderPage;
