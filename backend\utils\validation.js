const validator = require('validator');

// Email validation
const validateEmail = (email) => {
  return validator.isEmail(email);
};

// Password validation
const validatePassword = (password) => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

// Username validation
const validateUsername = (username) => {
  // 3-30 characters, alphanumeric, underscores, and hyphens only
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
  return usernameRegex.test(username);
};

// URL validation
const validateUrl = (url) => {
  return validator.isURL(url, {
    protocols: ['http', 'https'],
    require_protocol: true
  });
};

// Hex color validation
const validateHexColor = (color) => {
  const hexColorRegex = /^#[0-9A-F]{6}$/i;
  return hexColorRegex.test(color);
};

// Phone number validation
const validatePhoneNumber = (phone) => {
  return validator.isMobilePhone(phone);
};

// Domain validation
const validateDomain = (domain) => {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
};

// Sanitize HTML
const sanitizeHtml = (html) => {
  return validator.escape(html);
};

// Validate JSON
const validateJson = (jsonString) => {
  try {
    JSON.parse(jsonString);
    return true;
  } catch (error) {
    return false;
  }
};

// Validate price
const validatePrice = (price) => {
  const priceRegex = /^\d+(\.\d{1,2})?$/;
  return priceRegex.test(price.toString()) && parseFloat(price) >= 0;
};

// Validate currency code
const validateCurrency = (currency) => {
  const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'CNY', 'INR'];
  return validCurrencies.includes(currency.toUpperCase());
};

// Validate file extension
const validateFileExtension = (filename, allowedExtensions) => {
  const extension = filename.split('.').pop().toLowerCase();
  return allowedExtensions.includes(extension);
};

// Validate file size
const validateFileSize = (fileSize, maxSizeInMB) => {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return fileSize <= maxSizeInBytes;
};

// Validate API key format
const validateApiKey = (apiKey) => {
  // API keys should be 32-64 characters, alphanumeric
  const apiKeyRegex = /^[a-zA-Z0-9]{32,64}$/;
  return apiKeyRegex.test(apiKey);
};

// Validate webhook URL
const validateWebhookUrl = (url) => {
  return validateUrl(url) && (url.startsWith('https://') || process.env.NODE_ENV === 'development');
};

// Validate prompt length
const validatePromptLength = (prompt) => {
  return prompt && prompt.length >= 10 && prompt.length <= 10000;
};

// Validate agent name
const validateAgentName = (name) => {
  return name && name.length >= 2 && name.length <= 100;
};

// Validate temperature (AI parameter)
const validateTemperature = (temperature) => {
  return typeof temperature === 'number' && temperature >= 0 && temperature <= 2;
};

// Validate max tokens (AI parameter)
const validateMaxTokens = (maxTokens) => {
  return Number.isInteger(maxTokens) && maxTokens >= 1 && maxTokens <= 4000;
};

// Common validation errors
const ValidationErrors = {
  INVALID_EMAIL: 'Invalid email format',
  INVALID_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  INVALID_USERNAME: 'Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens',
  INVALID_URL: 'Invalid URL format',
  INVALID_HEX_COLOR: 'Invalid hex color format',
  INVALID_PHONE: 'Invalid phone number format',
  INVALID_DOMAIN: 'Invalid domain format',
  INVALID_JSON: 'Invalid JSON format',
  INVALID_PRICE: 'Invalid price format',
  INVALID_CURRENCY: 'Invalid currency code',
  INVALID_FILE_EXTENSION: 'Invalid file extension',
  INVALID_FILE_SIZE: 'File size exceeds limit',
  INVALID_API_KEY: 'Invalid API key format',
  INVALID_WEBHOOK_URL: 'Invalid webhook URL',
  INVALID_PROMPT_LENGTH: 'Prompt must be between 10 and 10,000 characters',
  INVALID_AGENT_NAME: 'Agent name must be between 2 and 100 characters',
  INVALID_TEMPERATURE: 'Temperature must be between 0 and 2',
  INVALID_MAX_TOKENS: 'Max tokens must be between 1 and 4,000'
};

module.exports = {
  validateEmail,
  validatePassword,
  validateUsername,
  validateUrl,
  validateHexColor,
  validatePhoneNumber,
  validateDomain,
  sanitizeHtml,
  validateJson,
  validatePrice,
  validateCurrency,
  validateFileExtension,
  validateFileSize,
  validateApiKey,
  validateWebhookUrl,
  validatePromptLength,
  validateAgentName,
  validateTemperature,
  validateMaxTokens,
  ValidationErrors
};
