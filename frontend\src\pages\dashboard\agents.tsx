import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';
import Layout from '@/components/common/Layout';
import { useAuth } from '@/context/AuthContext';
import { agentsAPI } from '@/utils/api';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ConfirmDialog from '@/components/common/ConfirmDialog';
import toast from 'react-hot-toast';

const AgentsPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [page, setPage] = useState(1);
  const [deleteAgent, setDeleteAgent] = useState<any>(null);

  // Fetch agents
  const { data: agentsData, isLoading, error } = useQuery(
    ['agents', { page, search: searchQuery, status: statusFilter, sortBy, sortOrder }],
    () => agentsAPI.getAll({
      page,
      limit: 12,
      search: searchQuery || undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      sortBy,
      sortOrder
    }),
    {
      keepPreviousData: true,
      retry: 1
    }
  );

  // Delete agent mutation
  const deleteAgentMutation = useMutation(
    (id: string) => agentsAPI.delete(id),
    {
      onSuccess: () => {
        toast.success('Agent deleted successfully');
        queryClient.invalidateQueries(['agents']);
        setDeleteAgent(null);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to delete agent');
      }
    }
  );

  // Toggle agent status mutation
  const toggleStatusMutation = useMutation(
    (id: string) => agentsAPI.toggleStatus(id),
    {
      onSuccess: () => {
        toast.success('Agent status updated');
        queryClient.invalidateQueries(['agents']);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update agent status');
      }
    }
  );

  // Clone agent mutation
  const cloneAgentMutation = useMutation(
    ({ id, name }: { id: string; name: string }) => agentsAPI.clone(id, { name }),
    {
      onSuccess: (response) => {
        toast.success('Agent cloned successfully');
        queryClient.invalidateQueries(['agents']);
        router.push(`/builder/${response.data.agent.id}`);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to clone agent');
      }
    }
  );

  const handleDeleteAgent = (agent: any) => {
    setDeleteAgent(agent);
  };

  const confirmDelete = () => {
    if (deleteAgent) {
      deleteAgentMutation.mutate(deleteAgent.id);
    }
  };

  const handleCloneAgent = (agent: any) => {
    const cloneName = `${agent.name} (Copy)`;
    cloneAgentMutation.mutate({ id: agent.id, name: cloneName });
  };

  const handleToggleStatus = (agent: any) => {
    toggleStatusMutation.mutate(agent.id);
  };

  const agents = agentsData?.data?.agents || [];
  const pagination = agentsData?.data?.pagination;

  if (!isAuthenticated) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-6">Please log in to access your agents.</p>
            <Link href="/auth/login" className="btn-primary">
              Sign In
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>My Agents - PromptCash</title>
        <meta name="description" content="Manage your AI agents and monitor their performance" />
      </Head>

      <Layout>
        <div className="container-custom py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Agents</h1>
              <p className="text-gray-600 mt-1">
                Create, manage, and deploy your AI agents
              </p>
            </div>
            <Link href="/builder/new" className="btn-primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Agent
            </Link>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search agents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>

              {/* Sort */}
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order);
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
              </select>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" text="Loading agents..." />
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load agents</h3>
              <p className="text-gray-600 mb-4">There was an error loading your agents. Please try again.</p>
              <button
                onClick={() => queryClient.invalidateQueries(['agents'])}
                className="btn-primary"
              >
                Retry
              </button>
            </div>
          )}

          {/* Agents Grid */}
          {!isLoading && !error && (
            <>
              {agents.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
                  <p className="text-gray-600 mb-6">
                    {searchQuery ? 'No agents match your search criteria.' : 'Get started by creating your first AI agent.'}
                  </p>
                  <Link href="/builder/new" className="btn-primary">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Create Your First Agent
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <AnimatePresence>
                    {agents.map((agent: any) => (
                      <motion.div
                        key={agent.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
                      >
                        <div className="p-6">
                          {/* Agent Header */}
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                {agent.avatar ? (
                                  <img src={agent.avatar} alt={agent.name} className="w-10 h-10 rounded-lg" />
                                ) : (
                                  <span className="text-primary-600 font-semibold">
                                    {agent.name.charAt(0).toUpperCase()}
                                  </span>
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-lg font-semibold text-gray-900 truncate">
                                  {agent.name}
                                </h3>
                                <div className="flex items-center space-x-2">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    agent.isActive
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-gray-100 text-gray-800'
                                  }`}>
                                    {agent.isActive ? 'Active' : 'Inactive'}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {agent.pricingType}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Actions Menu */}
                            <div className="relative group">
                              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                                <EllipsisVerticalIcon className="h-5 w-5" />
                              </button>
                              <div className="absolute right-0 top-8 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                                <Link
                                  href={`/builder/${agent.id}`}
                                  className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                  <span>Edit</span>
                                </Link>
                                <Link
                                  href={`/agents/${agent.id}`}
                                  className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <EyeIcon className="h-4 w-4" />
                                  <span>View</span>
                                </Link>
                                <button
                                  onClick={() => handleCloneAgent(agent)}
                                  className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <DocumentDuplicateIcon className="h-4 w-4" />
                                  <span>Clone</span>
                                </button>
                                <button
                                  onClick={() => handleToggleStatus(agent)}
                                  className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  {agent.isActive ? (
                                    <>
                                      <PauseIcon className="h-4 w-4" />
                                      <span>Deactivate</span>
                                    </>
                                  ) : (
                                    <>
                                      <PlayIcon className="h-4 w-4" />
                                      <span>Activate</span>
                                    </>
                                  )}
                                </button>
                                <hr className="my-1" />
                                <button
                                  onClick={() => handleDeleteAgent(agent)}
                                  className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                  <span>Delete</span>
                                </button>
                              </div>
                            </div>
                          </div>

                          {/* Agent Description */}
                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                            {agent.description || 'No description provided'}
                          </p>

                          {/* Agent Stats */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                {agent._count?.interactions || 0}
                              </div>
                              <div className="text-xs text-gray-500">Interactions</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                {agent._count?.subscriptions || 0}
                              </div>
                              <div className="text-xs text-gray-500">Subscribers</div>
                            </div>
                          </div>

                          {/* Agent Actions */}
                          <div className="flex space-x-2">
                            <Link
                              href={`/builder/${agent.id}`}
                              className="flex-1 btn-outline btn-sm text-center"
                            >
                              Edit
                            </Link>
                            <Link
                              href={`/agents/${agent.id}`}
                              className="flex-1 btn-primary btn-sm text-center"
                            >
                              View
                            </Link>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}

              {/* Pagination */}
              {pagination && pagination.pages > 1 && (
                <div className="flex items-center justify-between mt-8">
                  <div className="text-sm text-gray-700">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} agents
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setPage(page - 1)}
                      disabled={page <= 1}
                      className="btn-outline btn-sm disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setPage(page + 1)}
                      disabled={page >= pagination.pages}
                      className="btn-outline btn-sm disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          isOpen={!!deleteAgent}
          onClose={() => setDeleteAgent(null)}
          onConfirm={confirmDelete}
          title="Delete Agent"
          message={`Are you sure you want to delete "${deleteAgent?.name}"? This action cannot be undone and will remove all associated data.`}
          confirmText="Delete"
          confirmButtonClass="btn-danger"
          isLoading={deleteAgentMutation.isLoading}
        />
      </Layout>
    </>
  );
};

export default AgentsPage;
