version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: promptcash-postgres
    environment:
      POSTGRES_DB: promptcash
      POSTGRES_USER: promptcash
      POSTGRES_PASSWORD: promptcash123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - promptcash-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: promptcash-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - promptcash-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: promptcash-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ***************************************************/promptcash
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-change-in-production
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - promptcash-network
    command: npm run dev

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: promptcash-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5000
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - promptcash-network
    command: npm run dev

  # Widget Builder
  widget:
    build:
      context: ./widget
      dockerfile: Dockerfile
    container_name: promptcash-widget
    environment:
      NODE_ENV: development
      API_URL: http://localhost:5000
    ports:
      - "8080:8080"
    depends_on:
      - backend
    volumes:
      - ./widget:/app
      - /app/node_modules
    networks:
      - promptcash-network
    command: npm run dev

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: promptcash-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - promptcash-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  promptcash-network:
    driver: bridge
