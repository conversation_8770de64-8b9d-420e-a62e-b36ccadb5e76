import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMutation } from 'react-query';
import { 
  LightBulbIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChatBubbleLeftIcon,
  UserIcon,
  ComputerDesktopIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { agentsAPI } from '@/utils/api';
import toast from 'react-hot-toast';

interface AgentTesterProps {
  agentData: any;
  isNewAgent: boolean;
  onRemove?: () => void;
  canRemove?: boolean;
}

interface TestMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  tokens?: number;
  cost?: number;
}

const AgentTester: React.FC<AgentTesterProps> = ({
  agentData,
  isNewAgent,
  onRemove,
  canRemove = false
}) => {
  const [messages, setMessages] = useState<TestMessage[]>([]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Test agent mutation
  const testMutation = useMutation(
    (data: { input: string; context: any[] }) => {
      // For new agents, simulate the test locally
      if (isNewAgent) {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: {
                input: data.input,
                output: `This is a simulated response based on your current configuration. Your agent would respond according to the prompt: "${agentData.prompt.substring(0, 100)}..."`,
                tokens: Math.floor(Math.random() * 100) + 50,
                cost: 0.001
              }
            });
          }, 1000 + Math.random() * 2000);
        });
      }
      return agentsAPI.test(agentData.id, data);
    },
    {
      onSuccess: (response: any) => {
        const assistantMessage: TestMessage = {
          id: Date.now().toString() + '-assistant',
          role: 'assistant',
          content: response.data.output,
          timestamp: new Date(),
          tokens: response.data.tokens,
          cost: response.data.cost
        };
        setMessages(prev => [...prev, assistantMessage]);
        setIsTyping(false);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to test agent');
        setIsTyping(false);
      }
    }
  );

  const handleSendMessage = () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: TestMessage = {
      id: Date.now().toString() + '-user',
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Prepare context from previous messages
    const context = messages.slice(-6).map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // Test the agent
    testMutation.mutate({
      input: input.trim(),
      context
    });

    setInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearConversation = () => {
    setMessages([]);
    toast.success('Conversation cleared');
  };

  const totalTokens = messages.reduce((sum, msg) => sum + (msg.tokens || 0), 0);
  const totalCost = messages.reduce((sum, msg) => sum + (msg.cost || 0), 0);

  const suggestedQuestions = [
    "Hello, how can you help me?",
    "What are your capabilities?",
    "Can you explain your role?",
    "What makes you unique?"
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <LightBulbIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Agent Tester</h3>
            <p className="text-sm text-gray-500">
              {isNewAgent ? 'Test your agent configuration' : 'Test your live agent'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {messages.length > 0 && (
            <button
              onClick={clearConversation}
              className="btn-outline btn-sm"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Clear
            </button>
          )}
          {canRemove && onRemove && (
            <button
              onClick={onRemove}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      <div className="flex flex-col h-96">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <ChatBubbleLeftIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Start Testing</h4>
              <p className="text-gray-600 mb-4">
                Send a message to test how your agent responds
              </p>
              
              {/* Suggested questions */}
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">Try these questions:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {suggestedQuestions.map((question, index) => (
                    <button
                      key={index}
                      onClick={() => setInput(question)}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex max-w-xs lg:max-w-md ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* Avatar */}
                    <div className={`flex-shrink-0 ${message.role === 'user' ? 'ml-3' : 'mr-3'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.role === 'user' ? 'bg-primary-600' : 'bg-gray-600'
                      }`}>
                        {message.role === 'user' ? (
                          <UserIcon className="h-5 w-5 text-white" />
                        ) : (
                          <ComputerDesktopIcon className="h-5 w-5 text-white" />
                        )}
                      </div>
                    </div>

                    {/* Message bubble */}
                    <div className={`px-4 py-2 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      
                      {/* Message metadata */}
                      <div className={`flex items-center space-x-2 mt-1 text-xs ${
                        message.role === 'user' ? 'text-primary-100' : 'text-gray-500'
                      }`}>
                        <ClockIcon className="h-3 w-3" />
                        <span>{message.timestamp.toLocaleTimeString()}</span>
                        {message.tokens && (
                          <>
                            <span>•</span>
                            <span>{message.tokens} tokens</span>
                          </>
                        )}
                        {message.cost && (
                          <>
                            <CurrencyDollarIcon className="h-3 w-3" />
                            <span>${message.cost.toFixed(4)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}

          {/* Typing indicator */}
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-start"
            >
              <div className="flex mr-3">
                <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                  <ComputerDesktopIcon className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="bg-gray-100 rounded-lg px-4 py-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Stats */}
        {messages.length > 0 && (
          <div className="px-4 py-2 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{messages.length} messages</span>
              <div className="flex items-center space-x-4">
                <span>{totalTokens} total tokens</span>
                <span>${totalCost.toFixed(4)} estimated cost</span>
              </div>
            </div>
          </div>
        )}

        {/* Input */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              disabled={isTyping}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            />
            <button
              onClick={handleSendMessage}
              disabled={!input.trim() || isTyping}
              className="btn-primary px-3 py-2 disabled:opacity-50"
            >
              <PaperAirplaneIcon className="h-5 w-5" />
            </button>
          </div>
          
          {isNewAgent && (
            <p className="mt-2 text-xs text-gray-500">
              This is a simulation. Save your agent to test with real AI responses.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentTester;
