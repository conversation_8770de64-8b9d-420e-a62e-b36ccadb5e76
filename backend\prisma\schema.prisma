// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     Boolean  @default(false)
  emailVerifiedAt   DateTime?
  password          String
  role              UserRole @default(USER)
  status            UserStatus @default(ACTIVE)
  
  // Subscription info
  subscriptionId    String?
  subscriptionStatus SubscriptionStatus @default(FREE)
  subscriptionEndsAt DateTime?
  
  // White-label settings
  brandName         String?
  brandLogo         String?
  brandColor        String?
  customDomain      String?
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  agents            Agent[]
  subscriptions     Subscription[]
  payments          Payment[]
  analytics         Analytics[]
  refreshTokens     RefreshToken[]
  
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

model Agent {
  id              String      @id @default(cuid())
  name            String
  description     String?
  prompt          String
  personality     Json?       // JSON object for personality settings
  responseTemplates Json?     // JSON array of response templates
  avatar          String?
  isActive        Boolean     @default(true)
  isPublic        Boolean     @default(false)
  
  // Monetization settings
  pricingType     PricingType @default(FREE)
  price           Decimal?    @db.Decimal(10, 2)
  currency        String      @default("USD")
  
  // Deployment settings
  deploymentType  DeploymentType @default(WIDGET)
  widgetConfig    Json?       // Widget configuration
  apiKey          String?     @unique
  customDomain    String?
  
  // AI Configuration
  aiProvider      AIProvider  @default(OPENAI)
  model           String      @default("gpt-3.5-turbo")
  temperature     Float       @default(0.7)
  maxTokens       Int         @default(1000)
  
  // Usage limits
  dailyLimit      Int?
  monthlyLimit    Int?
  
  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // Relations
  userId          String
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  interactions    Interaction[]
  analytics       Analytics[]
  subscriptions   AgentSubscription[]
  
  @@map("agents")
}

model Interaction {
  id          String   @id @default(cuid())
  agentId     String
  userId      String?  // Null for anonymous users
  sessionId   String   // For tracking anonymous sessions
  
  input       String
  output      String
  tokens      Int      @default(0)
  cost        Decimal? @db.Decimal(10, 4)
  
  // Metadata
  ipAddress   String?
  userAgent   String?
  country     String?
  
  createdAt   DateTime @default(now())
  
  // Relations
  agent       Agent    @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@map("interactions")
}

model Subscription {
  id              String             @id @default(cuid())
  userId          String
  stripeCustomerId String?           @unique
  stripeSubscriptionId String?       @unique
  paypalSubscriptionId String?       @unique
  
  plan            SubscriptionPlan   @default(BASIC)
  status          SubscriptionStatus @default(ACTIVE)
  
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean          @default(false)
  canceledAt         DateTime?
  
  // Pricing
  amount          Decimal            @db.Decimal(10, 2)
  currency        String             @default("USD")
  interval        BillingInterval    @default(MONTHLY)
  
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  
  // Relations
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]
  
  @@map("subscriptions")
}

model AgentSubscription {
  id          String   @id @default(cuid())
  agentId     String
  userId      String?  // Null for anonymous purchases
  email       String?  // For anonymous purchases
  
  status      SubscriptionStatus @default(ACTIVE)
  amount      Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  
  // Relations
  agent       Agent    @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@map("agent_subscriptions")
}

model Payment {
  id              String        @id @default(cuid())
  userId          String
  subscriptionId  String?
  
  // Payment provider info
  stripePaymentId String?       @unique
  paypalPaymentId String?       @unique
  provider        PaymentProvider @default(STRIPE)
  
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  
  // Metadata
  description     String?
  metadata        Json?
  
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Relations
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  
  @@map("payments")
}

model Analytics {
  id          String   @id @default(cuid())
  userId      String
  agentId     String?
  
  // Metrics
  date        DateTime @db.Date
  interactions Int     @default(0)
  revenue     Decimal  @default(0) @db.Decimal(10, 2)
  users       Int      @default(0)
  
  // Detailed metrics (JSON)
  metrics     Json?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  agent       Agent?   @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@unique([userId, agentId, date])
  @@map("analytics")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

enum SubscriptionStatus {
  FREE
  ACTIVE
  PAST_DUE
  CANCELED
  UNPAID
}

enum SubscriptionPlan {
  FREE
  BASIC
  PRO
  ENTERPRISE
}

enum BillingInterval {
  MONTHLY
  YEARLY
}

enum PricingType {
  FREE
  ONE_TIME
  SUBSCRIPTION
  PAY_PER_USE
}

enum DeploymentType {
  WIDGET
  STANDALONE
  API
}

enum AIProvider {
  OPENAI
  ANTHROPIC
  CUSTOM
}

enum PaymentProvider {
  STRIPE
  PAYPAL
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELED
}
