import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { motion } from 'framer-motion';
import { 
  Cog6ToothIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface BuilderSidebarProps {
  availableComponents: any[];
  activeComponents: string[];
  agentData: any;
  onAgentDataChange: (field: string, value: any) => void;
  onSave: () => void;
  isLoading: boolean;
}

const DraggableComponent: React.FC<{ component: any; isActive: boolean }> = ({ 
  component, 
  isActive 
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: component.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`p-3 rounded-lg border-2 border-dashed cursor-move transition-all ${
        isDragging
          ? 'border-primary-500 bg-primary-50 shadow-lg'
          : isActive
          ? 'border-green-300 bg-green-50'
          : 'border-gray-300 hover:border-primary-300 hover:bg-primary-50'
      }`}
    >
      <div className="flex items-center space-x-3">
        <component.icon className={`h-5 w-5 ${
          isActive ? 'text-green-600' : 'text-gray-600'
        }`} />
        <div className="flex-1 min-w-0">
          <p className={`text-sm font-medium ${
            isActive ? 'text-green-900' : 'text-gray-900'
          }`}>
            {component.title}
          </p>
          {component.required && (
            <p className="text-xs text-gray-500">Required</p>
          )}
        </div>
        {isActive && (
          <CheckCircleIcon className="h-4 w-4 text-green-600 flex-shrink-0" />
        )}
      </div>
    </div>
  );
};

const BuilderSidebar: React.FC<BuilderSidebarProps> = ({
  availableComponents,
  activeComponents,
  agentData,
  onAgentDataChange,
  onSave,
  isLoading
}) => {
  const [showBasicSettings, setShowBasicSettings] = useState(true);
  const [showAISettings, setShowAISettings] = useState(false);

  const validateAgent = () => {
    const issues = [];
    
    if (!agentData.name.trim()) {
      issues.push('Agent name is required');
    }
    
    if (!agentData.prompt.trim()) {
      issues.push('Prompt is required');
    } else if (agentData.prompt.length < 10) {
      issues.push('Prompt is too short');
    }
    
    if (agentData.temperature < 0 || agentData.temperature > 2) {
      issues.push('Temperature must be between 0 and 2');
    }
    
    if (agentData.maxTokens < 1 || agentData.maxTokens > 4000) {
      issues.push('Max tokens must be between 1 and 4000');
    }

    return issues;
  };

  const validationIssues = validateAgent();
  const isValid = validationIssues.length === 0;

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Builder Tools</h2>
        <p className="text-sm text-gray-500">Drag components to build your agent</p>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Components */}
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Components</h3>
          <div className="space-y-2">
            {availableComponents.map((component) => (
              <DraggableComponent
                key={component.id}
                component={component}
                isActive={activeComponents.includes(component.id)}
              />
            ))}
          </div>
        </div>

        {/* Basic Settings */}
        <div className="border-t border-gray-200">
          <button
            onClick={() => setShowBasicSettings(!showBasicSettings)}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50"
          >
            <div className="flex items-center space-x-2">
              <Cog6ToothIcon className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Basic Settings</span>
            </div>
            <motion.div
              animate={{ rotate: showBasicSettings ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <svg className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </motion.div>
          </button>

          {showBasicSettings && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4 space-y-4"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Agent Name *
                </label>
                <input
                  type="text"
                  value={agentData.name}
                  onChange={(e) => onAgentDataChange('name', e.target.value)}
                  placeholder="My AI Assistant"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={agentData.description}
                  onChange={(e) => onAgentDataChange('description', e.target.value)}
                  placeholder="Describe what your agent does..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pricing Type
                </label>
                <select
                  value={agentData.pricingType}
                  onChange={(e) => onAgentDataChange('pricingType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                >
                  <option value="FREE">Free</option>
                  <option value="ONE_TIME">One-time Payment</option>
                  <option value="SUBSCRIPTION">Subscription</option>
                  <option value="PAY_PER_USE">Pay Per Use</option>
                </select>
              </div>

              {agentData.pricingType !== 'FREE' && (
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price
                    </label>
                    <input
                      type="number"
                      value={agentData.price || ''}
                      onChange={(e) => onAgentDataChange('price', parseFloat(e.target.value) || null)}
                      placeholder="9.99"
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={agentData.currency}
                      onChange={(e) => onAgentDataChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                      <option value="CAD">CAD</option>
                      <option value="AUD">AUD</option>
                    </select>
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </div>

        {/* AI Settings */}
        <div className="border-t border-gray-200">
          <button
            onClick={() => setShowAISettings(!showAISettings)}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50"
          >
            <div className="flex items-center space-x-2">
              <Cog6ToothIcon className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">AI Settings</span>
            </div>
            <motion.div
              animate={{ rotate: showAISettings ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <svg className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </motion.div>
          </button>

          {showAISettings && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="px-4 pb-4 space-y-4"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  AI Provider
                </label>
                <select
                  value={agentData.aiProvider}
                  onChange={(e) => onAgentDataChange('aiProvider', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                >
                  <option value="OPENAI">OpenAI</option>
                  <option value="ANTHROPIC">Anthropic</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Model
                </label>
                <select
                  value={agentData.model}
                  onChange={(e) => onAgentDataChange('model', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                >
                  {agentData.aiProvider === 'OPENAI' ? (
                    <>
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                      <option value="gpt-4">GPT-4</option>
                      <option value="gpt-4-turbo">GPT-4 Turbo</option>
                    </>
                  ) : (
                    <>
                      <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                      <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                      <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                    </>
                  )}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Temperature: {agentData.temperature}
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={agentData.temperature}
                  onChange={(e) => onAgentDataChange('temperature', parseFloat(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Focused</span>
                  <span>Balanced</span>
                  <span>Creative</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Tokens
                </label>
                <input
                  type="number"
                  value={agentData.maxTokens}
                  onChange={(e) => onAgentDataChange('maxTokens', parseInt(e.target.value) || 1000)}
                  min="1"
                  max="4000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum response length (1-4000)
                </p>
              </div>
            </motion.div>
          )}
        </div>

        {/* Validation */}
        <div className="border-t border-gray-200 p-4">
          <div className={`p-3 rounded-lg ${
            isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-start space-x-2">
              {isValid ? (
                <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              )}
              <div className="flex-1">
                <p className={`text-sm font-medium ${
                  isValid ? 'text-green-800' : 'text-red-800'
                }`}>
                  {isValid ? 'Ready to save' : 'Issues found'}
                </p>
                {!isValid && (
                  <ul className="mt-1 text-sm text-red-700 space-y-1">
                    {validationIssues.map((issue, index) => (
                      <li key={index} className="flex items-start space-x-1">
                        <span className="mt-1.5 w-1 h-1 bg-red-600 rounded-full flex-shrink-0"></span>
                        <span>{issue}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={onSave}
          disabled={!isValid || isLoading}
          className="w-full btn-primary disabled:opacity-50"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="spinner w-4 h-4"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Agent'
          )}
        </button>
      </div>
    </div>
  );
};

export default BuilderSidebar;
