{"name": "promptcash", "version": "1.0.0", "description": "A comprehensive platform for building, deploying, and monetizing AI agents from custom prompts", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:widget\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:widget": "cd widget && npm run dev", "build": "npm run build:backend && npm run build:frontend && npm run build:widget", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:widget": "cd widget && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:e2e": "cd tests && npx cypress run", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "setup": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../widget && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules widget/node_modules", "migrate": "cd backend && npx prisma migrate dev", "seed": "cd backend && npx prisma db seed"}, "keywords": ["ai", "agents", "monetization", "prompts", "chatbots", "saas", "platform"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/promptcash.git"}, "bugs": {"url": "https://github.com/HectorTa1989/promptcash/issues"}, "homepage": "https://github.com/HectorTa1989/promptcash#readme", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["backend", "frontend", "widget"]}