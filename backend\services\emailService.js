const sgMail = require('@sendgrid/mail');

// Initialize SendGrid
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';
const SUPPORT_EMAIL = process.env.SUPPORT_EMAIL || '<EMAIL>';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

// Email templates
const emailTemplates = {
  verification: {
    subject: 'Verify Your PromptCash Account',
    html: (verificationUrl, firstName) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Account</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to PromptCash!</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName || 'there'}!</h2>
            <p>Thank you for signing up for PromptCash. To complete your registration, please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #3B82F6;">${verificationUrl}</p>
            <p>This verification link will expire in 24 hours.</p>
            <p>If you didn't create an account with PromptCash, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 PromptCash. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${SUPPORT_EMAIL}">${SUPPORT_EMAIL}</a></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  passwordReset: {
    subject: 'Reset Your PromptCash Password',
    html: (resetUrl, firstName) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
          .warning { background: #FEF3C7; border: 1px solid #F59E0B; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName || 'there'}!</h2>
            <p>We received a request to reset your PromptCash account password. Click the button below to reset it:</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #3B82F6;">${resetUrl}</p>
            <div class="warning">
              <strong>Security Notice:</strong> This password reset link will expire in 1 hour. If you didn't request this reset, please ignore this email and your password will remain unchanged.
            </div>
            <p>For security reasons, we recommend choosing a strong password that you haven't used before.</p>
          </div>
          <div class="footer">
            <p>© 2024 PromptCash. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${SUPPORT_EMAIL}">${SUPPORT_EMAIL}</a></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  welcome: {
    subject: 'Welcome to PromptCash - Start Building AI Agents!',
    html: (firstName, dashboardUrl) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to PromptCash</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .feature { background: #F3F4F6; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to PromptCash!</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName || 'there'}!</h2>
            <p>Congratulations! Your email has been verified and your PromptCash account is now active. You're ready to start building and monetizing AI agents!</p>
            
            <p style="text-align: center;">
              <a href="${dashboardUrl}" class="button">Go to Dashboard</a>
            </p>

            <h3>What you can do now:</h3>
            
            <div class="feature">
              <strong>🤖 Build AI Agents</strong><br>
              Use our drag-and-drop builder to create custom AI agents with your own prompts and personality.
            </div>
            
            <div class="feature">
              <strong>💰 Monetize Your Creations</strong><br>
              Set up subscriptions, one-time payments, or pay-per-use pricing for your agents.
            </div>
            
            <div class="feature">
              <strong>🚀 Deploy Anywhere</strong><br>
              Embed your agents as widgets, deploy as standalone apps, or use our API.
            </div>
            
            <div class="feature">
              <strong>📊 Track Performance</strong><br>
              Monitor usage, revenue, and engagement with real-time analytics.
            </div>

            <p>Need help getting started? Check out our <a href="${FRONTEND_URL}/docs">documentation</a> or contact our support team.</p>
          </div>
          <div class="footer">
            <p>© 2024 PromptCash. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${SUPPORT_EMAIL}">${SUPPORT_EMAIL}</a></p>
          </div>
        </div>
      </body>
      </html>
    `
  }
};

// Send verification email
const sendVerificationEmail = async (email, token, firstName) => {
  try {
    const verificationUrl = `${FRONTEND_URL}/auth/verify-email/${token}`;
    
    const msg = {
      to: email,
      from: FROM_EMAIL,
      subject: emailTemplates.verification.subject,
      html: emailTemplates.verification.html(verificationUrl, firstName)
    };

    await sgMail.send(msg);
    console.log(`Verification email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    throw error;
  }
};

// Send password reset email
const sendPasswordResetEmail = async (email, token, firstName) => {
  try {
    const resetUrl = `${FRONTEND_URL}/auth/reset-password?token=${token}`;
    
    const msg = {
      to: email,
      from: FROM_EMAIL,
      subject: emailTemplates.passwordReset.subject,
      html: emailTemplates.passwordReset.html(resetUrl, firstName)
    };

    await sgMail.send(msg);
    console.log(`Password reset email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    throw error;
  }
};

// Send welcome email
const sendWelcomeEmail = async (email, firstName) => {
  try {
    const dashboardUrl = `${FRONTEND_URL}/dashboard`;
    
    const msg = {
      to: email,
      from: FROM_EMAIL,
      subject: emailTemplates.welcome.subject,
      html: emailTemplates.welcome.html(firstName, dashboardUrl)
    };

    await sgMail.send(msg);
    console.log(`Welcome email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    throw error;
  }
};

// Send notification email
const sendNotificationEmail = async (email, subject, message, firstName) => {
  try {
    const msg = {
      to: email,
      from: FROM_EMAIL,
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Hi ${firstName || 'there'}!</h2>
          <p>${message}</p>
          <hr>
          <p style="font-size: 14px; color: #666;">
            © 2024 PromptCash. All rights reserved.<br>
            Need help? Contact us at <a href="mailto:${SUPPORT_EMAIL}">${SUPPORT_EMAIL}</a>
          </p>
        </div>
      `
    };

    await sgMail.send(msg);
    console.log(`Notification email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Failed to send notification email:', error);
    throw error;
  }
};

// Test email configuration
const testEmailConfiguration = async () => {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SENDGRID_API_KEY not configured');
    }

    // Test with a simple email to the support address
    const msg = {
      to: SUPPORT_EMAIL,
      from: FROM_EMAIL,
      subject: 'PromptCash Email Configuration Test',
      text: 'This is a test email to verify SendGrid configuration.'
    };

    await sgMail.send(msg);
    console.log('Email configuration test successful');
    return true;
  } catch (error) {
    console.error('Email configuration test failed:', error);
    return false;
  }
};

module.exports = {
  sendVerificationEmail,
  sendPasswordResetEmail,
  sendWelcomeEmail,
  sendNotificationEmail,
  testEmailConfiguration
};
