# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/promptcash"
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_SECRET="your-refresh-token-secret"
REFRESH_TOKEN_EXPIRES_IN="30d"

# AI API Keys
OPENAI_API_KEY="sk-your-openai-api-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"
OPENAI_ORG_ID="org-your-openai-org-id"

# Payment Processing
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_MODE="sandbox"

# Email Service (SendGrid)
SENDGRID_API_KEY="SG.your-sendgrid-api-key"
FROM_EMAIL="<EMAIL>"
SUPPORT_EMAIL="<EMAIL>"

# File Storage (AWS S3 or compatible)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
S3_BUCKET_NAME="promptcash-storage"
S3_ENDPOINT="https://s3.amazonaws.com"

# Application Configuration
NODE_ENV="development"
PORT=5000
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:5000"
WIDGET_URL="http://localhost:8080"

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN="http://localhost:3000"

# Analytics & Monitoring
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
SENTRY_DSN="https://your-sentry-dsn"

# CDN Configuration
CDN_URL="https://cdn.promptcash.com"
STATIC_FILES_URL="https://static.promptcash.com"

# White-label Configuration
ENABLE_WHITE_LABEL=true
DEFAULT_BRAND_NAME="PromptCash"
DEFAULT_BRAND_COLOR="#3B82F6"

# Feature Flags
ENABLE_PAYPAL=true
ENABLE_ANALYTICS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_FILE_UPLOADS=true
ENABLE_CUSTOM_DOMAINS=false

# Development
DEBUG=true
LOG_LEVEL="debug"
ENABLE_SWAGGER=true
