const { PrismaClient } = require('@prisma/client');

let prisma;

// Singleton pattern for Prisma client
function getPrismaClient() {
  if (!prisma) {
    prisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
  }
  return prisma;
}

// Connect to database
async function connectDatabase() {
  try {
    const client = getPrismaClient();
    await client.$connect();
    
    // Test the connection
    await client.$queryRaw`SELECT 1`;
    
    console.log('Database connection established successfully');
    return client;
  } catch (error) {
    console.error('Failed to connect to database:', error);
    throw error;
  }
}

// Disconnect from database
async function disconnectDatabase() {
  try {
    if (prisma) {
      await prisma.$disconnect();
      console.log('Database connection closed');
    }
  } catch (error) {
    console.error('Error disconnecting from database:', error);
    throw error;
  }
}

// Health check for database
async function checkDatabaseHealth() {
  try {
    const client = getPrismaClient();
    await client.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
}

// Transaction wrapper
async function withTransaction(callback) {
  const client = getPrismaClient();
  return await client.$transaction(callback);
}

module.exports = {
  getPrismaClient,
  connectDatabase,
  disconnectDatabase,
  checkDatabaseHealth,
  withTransaction
};
