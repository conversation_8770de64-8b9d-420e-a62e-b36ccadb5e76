const jwt = require('jsonwebtoken');
const { getPrismaClient } = require('../config/database');
const { cache } = require('../config/redis');

const prisma = getPrismaClient();

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        code: 'TOKEN_REQUIRED'
      });
    }

    // Check if token is blacklisted (cached)
    const isBlacklisted = await cache.get(`blacklist:${token}`);
    if (isBlacklisted) {
      return res.status(401).json({
        error: 'Token has been revoked',
        code: 'TOKEN_REVOKED'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        subscriptionStatus: true,
        emailVerified: true
      }
    });

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    if (user.status !== 'ACTIVE') {
      return res.status(401).json({
        error: 'Account is not active',
        code: 'ACCOUNT_INACTIVE'
      });
    }

    // Attach user to request
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    }

    console.error('Auth middleware error:', error);
    return res.status(500).json({
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        subscriptionStatus: true
      }
    });

    req.user = user && user.status === 'ACTIVE' ? user : null;
    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// Role-based authorization
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    next();
  };
};

// Subscription-based authorization
const requireSubscription = (plans) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const requiredPlans = Array.isArray(plans) ? plans : [plans];
    
    if (!requiredPlans.includes(req.user.subscriptionStatus)) {
      return res.status(403).json({
        error: 'Subscription upgrade required',
        code: 'SUBSCRIPTION_REQUIRED',
        requiredPlans
      });
    }

    next();
  };
};

// Email verification requirement
const requireEmailVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.emailVerified) {
    return res.status(403).json({
      error: 'Email verification required',
      code: 'EMAIL_VERIFICATION_REQUIRED'
    });
  }

  next();
};

// Rate limiting per user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return async (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const key = `rate_limit:${req.user.id}`;
    const current = await cache.incr(key);
    
    if (current === 1) {
      await cache.expire(key, Math.ceil(windowMs / 1000));
    }

    if (current > maxRequests) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: windowMs / 1000
      });
    }

    res.set({
      'X-RateLimit-Limit': maxRequests,
      'X-RateLimit-Remaining': Math.max(0, maxRequests - current),
      'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString()
    });

    next();
  };
};

// Agent ownership verification
const requireAgentOwnership = async (req, res, next) => {
  try {
    const agentId = req.params.id || req.params.agentId || req.body.agentId;
    
    if (!agentId) {
      return res.status(400).json({
        error: 'Agent ID required',
        code: 'AGENT_ID_REQUIRED'
      });
    }

    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      select: { userId: true }
    });

    if (!agent) {
      return res.status(404).json({
        error: 'Agent not found',
        code: 'AGENT_NOT_FOUND'
      });
    }

    if (agent.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED'
      });
    }

    next();
  } catch (error) {
    console.error('Agent ownership verification error:', error);
    return res.status(500).json({
      error: 'Authorization failed',
      code: 'AUTH_ERROR'
    });
  }
};

module.exports = {
  verifyToken,
  optionalAuth,
  requireRole,
  requireSubscription,
  requireEmailVerification,
  userRateLimit,
  requireAgentOwnership
};
