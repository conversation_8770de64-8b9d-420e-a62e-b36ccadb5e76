import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  SparklesIcon,
  PlusIcon,
  XMarkIcon,
  FaceSmileIcon,
  ChatBubbleBottomCenterTextIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

interface PersonalityConfigProps {
  personality: {
    traits: string[];
    tone: string;
    style: string;
    customInstructions: string;
  };
  onChange: (personality: any) => void;
  onRemove?: () => void;
  canRemove?: boolean;
}

const personalityTraits = [
  'Friendly', 'Professional', 'Enthusiastic', 'Calm', 'Witty', 'Empathetic',
  'Analytical', 'Creative', 'Patient', 'Energetic', 'Thoughtful', 'Confident',
  'Supportive', 'Curious', 'Diplomatic', 'Direct', 'Optimistic', 'Realistic'
];

const toneOptions = [
  { value: 'professional', label: 'Professional', description: 'Formal and business-appropriate' },
  { value: 'friendly', label: 'Friendly', description: 'Warm and approachable' },
  { value: 'casual', label: 'Casual', description: 'Relaxed and conversational' },
  { value: 'formal', label: 'Formal', description: 'Structured and official' },
  { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic and positive' },
  { value: 'calm', label: 'Calm', description: 'Peaceful and reassuring' }
];

const styleOptions = [
  { value: 'helpful', label: 'Helpful', description: 'Focus on being useful and supportive' },
  { value: 'educational', label: 'Educational', description: 'Teach and explain concepts clearly' },
  { value: 'creative', label: 'Creative', description: 'Think outside the box and be innovative' },
  { value: 'analytical', label: 'Analytical', description: 'Logical and data-driven approach' },
  { value: 'conversational', label: 'Conversational', description: 'Natural dialogue and discussion' },
  { value: 'concise', label: 'Concise', description: 'Brief and to-the-point responses' }
];

const PersonalityConfig: React.FC<PersonalityConfigProps> = ({
  personality,
  onChange,
  onRemove,
  canRemove = false
}) => {
  const [newTrait, setNewTrait] = useState('');
  const [showCustomTraits, setShowCustomTraits] = useState(false);

  const updatePersonality = (field: string, value: any) => {
    onChange({
      ...personality,
      [field]: value
    });
  };

  const addTrait = (trait: string) => {
    if (trait && !personality.traits.includes(trait)) {
      updatePersonality('traits', [...personality.traits, trait]);
    }
  };

  const removeTrait = (trait: string) => {
    updatePersonality('traits', personality.traits.filter(t => t !== trait));
  };

  const addCustomTrait = () => {
    if (newTrait.trim() && !personality.traits.includes(newTrait.trim())) {
      addTrait(newTrait.trim());
      setNewTrait('');
      setShowCustomTraits(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <SparklesIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Personality Settings</h3>
            <p className="text-sm text-gray-500">Define your agent's personality and communication style</p>
          </div>
        </div>
        {canRemove && onRemove && (
          <button
            onClick={onRemove}
            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="p-4 space-y-6">
        {/* Personality Traits */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <FaceSmileIcon className="h-5 w-5 text-gray-600" />
            <label className="text-sm font-medium text-gray-700">
              Personality Traits
            </label>
          </div>
          
          {/* Selected traits */}
          <div className="flex flex-wrap gap-2 mb-3">
            <AnimatePresence>
              {personality.traits.map((trait) => (
                <motion.span
                  key={trait}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                >
                  {trait}
                  <button
                    onClick={() => removeTrait(trait)}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </motion.span>
              ))}
            </AnimatePresence>
          </div>

          {/* Available traits */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mb-3">
            {personalityTraits
              .filter(trait => !personality.traits.includes(trait))
              .map((trait) => (
                <button
                  key={trait}
                  onClick={() => addTrait(trait)}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors text-left"
                >
                  {trait}
                </button>
              ))}
          </div>

          {/* Add custom trait */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowCustomTraits(!showCustomTraits)}
              className="flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Add custom trait</span>
            </button>
          </div>

          {showCustomTraits && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-2 flex space-x-2"
            >
              <input
                type="text"
                value={newTrait}
                onChange={(e) => setNewTrait(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addCustomTrait()}
                placeholder="Enter custom trait"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                onClick={addCustomTrait}
                className="btn-primary btn-sm"
              >
                Add
              </button>
              <button
                onClick={() => {
                  setShowCustomTraits(false);
                  setNewTrait('');
                }}
                className="btn-outline btn-sm"
              >
                Cancel
              </button>
            </motion.div>
          )}
        </div>

        {/* Communication Tone */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <ChatBubbleBottomCenterTextIcon className="h-5 w-5 text-gray-600" />
            <label className="text-sm font-medium text-gray-700">
              Communication Tone
            </label>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {toneOptions.map((option) => (
              <label
                key={option.value}
                className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                  personality.tone === option.value
                    ? 'border-primary-600 ring-2 ring-primary-600 bg-primary-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input
                  type="radio"
                  name="tone"
                  value={option.value}
                  checked={personality.tone === option.value}
                  onChange={(e) => updatePersonality('tone', e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">{option.label}</div>
                      <div className="text-gray-500">{option.description}</div>
                    </div>
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Communication Style */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-600" />
            <label className="text-sm font-medium text-gray-700">
              Communication Style
            </label>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {styleOptions.map((option) => (
              <label
                key={option.value}
                className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                  personality.style === option.value
                    ? 'border-primary-600 ring-2 ring-primary-600 bg-primary-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input
                  type="radio"
                  name="style"
                  value={option.value}
                  checked={personality.style === option.value}
                  onChange={(e) => updatePersonality('style', e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">{option.label}</div>
                      <div className="text-gray-500">{option.description}</div>
                    </div>
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Custom Instructions */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Custom Instructions
          </label>
          <textarea
            value={personality.customInstructions}
            onChange={(e) => updatePersonality('customInstructions', e.target.value)}
            placeholder="Add any specific instructions for how your agent should behave..."
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
          <p className="mt-1 text-sm text-gray-500">
            These instructions will be added to your agent's system prompt
          </p>
        </div>

        {/* Preview */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Personality Preview</h4>
          <div className="text-sm text-gray-600 space-y-1">
            {personality.traits.length > 0 && (
              <p><strong>Traits:</strong> {personality.traits.join(', ')}</p>
            )}
            <p><strong>Tone:</strong> {toneOptions.find(t => t.value === personality.tone)?.label}</p>
            <p><strong>Style:</strong> {styleOptions.find(s => s.value === personality.style)?.label}</p>
            {personality.customInstructions && (
              <p><strong>Custom:</strong> {personality.customInstructions}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonalityConfig;
