import React, { useState, useEffect } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PlusIcon, 
  SparklesIcon, 
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import PromptEditor from './PromptEditor';
import PersonalityConfig from './PersonalityConfig';
import ResponseTemplates from './ResponseTemplates';
import AgentTester from './AgentTester';
import PromptOptimizer from './PromptOptimizer';
import BuilderSidebar from './BuilderSidebar';
import DraggableComponent from './DraggableComponent';
import toast from 'react-hot-toast';

interface AgentBuilderProps {
  agent?: any;
  onSave: (data: any) => void;
  isLoading: boolean;
  isNewAgent: boolean;
}

interface BuilderComponent {
  id: string;
  type: 'prompt' | 'personality' | 'templates' | 'tester' | 'optimizer';
  title: string;
  icon: React.ComponentType<any>;
  required?: boolean;
}

const availableComponents: BuilderComponent[] = [
  {
    id: 'prompt',
    type: 'prompt',
    title: 'Prompt Configuration',
    icon: ChatBubbleLeftRightIcon,
    required: true
  },
  {
    id: 'personality',
    type: 'personality',
    title: 'Personality Settings',
    icon: SparklesIcon
  },
  {
    id: 'templates',
    type: 'templates',
    title: 'Response Templates',
    icon: Cog6ToothIcon
  },
  {
    id: 'tester',
    type: 'tester',
    title: 'Agent Tester',
    icon: LightBulbIcon
  },
  {
    id: 'optimizer',
    type: 'optimizer',
    title: 'Prompt Optimizer',
    icon: SparklesIcon
  }
];

const AgentBuilder: React.FC<AgentBuilderProps> = ({
  agent,
  onSave,
  isLoading,
  isNewAgent
}) => {
  const [activeComponents, setActiveComponents] = useState<string[]>(['prompt']);
  const [agentData, setAgentData] = useState({
    name: '',
    description: '',
    prompt: '',
    personality: {
      traits: [],
      tone: 'professional',
      style: 'helpful',
      customInstructions: ''
    },
    responseTemplates: [],
    aiProvider: 'OPENAI',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1000,
    pricingType: 'FREE',
    price: null,
    currency: 'USD'
  });

  const [draggedComponent, setDraggedComponent] = useState<BuilderComponent | null>(null);
  const [showOptimizer, setShowOptimizer] = useState(false);

  // Initialize agent data
  useEffect(() => {
    if (agent) {
      setAgentData({
        name: agent.name || '',
        description: agent.description || '',
        prompt: agent.prompt || '',
        personality: agent.personality || {
          traits: [],
          tone: 'professional',
          style: 'helpful',
          customInstructions: ''
        },
        responseTemplates: agent.responseTemplates || [],
        aiProvider: agent.aiProvider || 'OPENAI',
        model: agent.model || 'gpt-3.5-turbo',
        temperature: agent.temperature || 0.7,
        maxTokens: agent.maxTokens || 1000,
        pricingType: agent.pricingType || 'FREE',
        price: agent.price,
        currency: agent.currency || 'USD'
      });

      // Set active components based on existing data
      const components = ['prompt'];
      if (agent.personality && Object.keys(agent.personality).length > 0) {
        components.push('personality');
      }
      if (agent.responseTemplates && agent.responseTemplates.length > 0) {
        components.push('templates');
      }
      setActiveComponents(components);
    }
  }, [agent]);

  const handleDragStart = (event: DragStartEvent) => {
    const component = availableComponents.find(c => c.id === event.active.id);
    setDraggedComponent(component || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedComponent(null);

    if (over && over.id === 'builder-area') {
      const componentId = active.id as string;
      if (!activeComponents.includes(componentId)) {
        setActiveComponents(prev => [...prev, componentId]);
        toast.success('Component added to builder');
      }
    }
  };

  const removeComponent = (componentId: string) => {
    const component = availableComponents.find(c => c.id === componentId);
    if (component?.required) {
      toast.error('This component is required and cannot be removed');
      return;
    }
    
    setActiveComponents(prev => prev.filter(id => id !== componentId));
    toast.success('Component removed from builder');
  };

  const updateAgentData = (field: string, value: any) => {
    setAgentData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Validation
    if (!agentData.name.trim()) {
      toast.error('Agent name is required');
      return;
    }

    if (!agentData.prompt.trim()) {
      toast.error('Prompt is required');
      return;
    }

    if (agentData.prompt.length < 10) {
      toast.error('Prompt must be at least 10 characters long');
      return;
    }

    onSave(agentData);
  };

  const renderComponent = (componentId: string) => {
    const component = availableComponents.find(c => c.id === componentId);
    if (!component) return null;

    const commonProps = {
      onRemove: () => removeComponent(componentId),
      canRemove: !component.required
    };

    switch (component.type) {
      case 'prompt':
        return (
          <PromptEditor
            key={componentId}
            prompt={agentData.prompt}
            onChange={(value) => updateAgentData('prompt', value)}
            onOptimize={() => setShowOptimizer(true)}
            {...commonProps}
          />
        );
      
      case 'personality':
        return (
          <PersonalityConfig
            key={componentId}
            personality={agentData.personality}
            onChange={(value) => updateAgentData('personality', value)}
            {...commonProps}
          />
        );
      
      case 'templates':
        return (
          <ResponseTemplates
            key={componentId}
            templates={agentData.responseTemplates}
            onChange={(value) => updateAgentData('responseTemplates', value)}
            {...commonProps}
          />
        );
      
      case 'tester':
        return (
          <AgentTester
            key={componentId}
            agentData={agentData}
            isNewAgent={isNewAgent}
            {...commonProps}
          />
        );
      
      case 'optimizer':
        return (
          <PromptOptimizer
            key={componentId}
            prompt={agentData.prompt}
            onOptimize={(optimizedPrompt) => {
              updateAgentData('prompt', optimizedPrompt);
              toast.success('Prompt optimized successfully');
            }}
            {...commonProps}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      <div className="flex h-full">
        {/* Sidebar */}
        <BuilderSidebar
          availableComponents={availableComponents}
          activeComponents={activeComponents}
          agentData={agentData}
          onAgentDataChange={updateAgentData}
          onSave={handleSave}
          isLoading={isLoading}
        />

        {/* Main builder area */}
        <div className="flex-1 bg-gray-50">
          <div 
            id="builder-area"
            className="h-full p-6 overflow-y-auto"
          >
            <div className="max-w-4xl mx-auto space-y-6">
              {/* Header */}
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {isNewAgent ? 'Build Your AI Agent' : 'Edit Your Agent'}
                </h2>
                <p className="text-gray-600">
                  Drag components from the sidebar or configure existing ones below
                </p>
              </div>

              {/* Active components */}
              <SortableContext 
                items={activeComponents} 
                strategy={verticalListSortingStrategy}
              >
                <AnimatePresence>
                  {activeComponents.map((componentId) => (
                    <motion.div
                      key={componentId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      <DraggableComponent id={componentId}>
                        {renderComponent(componentId)}
                      </DraggableComponent>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </SortableContext>

              {/* Empty state */}
              {activeComponents.length === 0 && (
                <div className="text-center py-12">
                  <PlusIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No components added yet
                  </h3>
                  <p className="text-gray-600">
                    Drag components from the sidebar to start building your agent
                  </p>
                </div>
              )}

              {/* Drop zone indicator */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
                <PlusIcon className="h-8 w-8 mx-auto mb-2" />
                <p>Drop components here to add them to your agent</p>
              </div>
            </div>
          </div>
        </div>

        {/* Drag overlay */}
        <DragOverlay>
          {draggedComponent && (
            <div className="bg-white rounded-lg shadow-lg border-2 border-primary-500 p-4">
              <div className="flex items-center space-x-3">
                <draggedComponent.icon className="h-6 w-6 text-primary-600" />
                <span className="font-medium text-gray-900">
                  {draggedComponent.title}
                </span>
              </div>
            </div>
          )}
        </DragOverlay>

        {/* Prompt Optimizer Modal */}
        {showOptimizer && (
          <PromptOptimizer
            prompt={agentData.prompt}
            onOptimize={(optimizedPrompt) => {
              updateAgentData('prompt', optimizedPrompt);
              setShowOptimizer(false);
              toast.success('Prompt optimized successfully');
            }}
            onClose={() => setShowOptimizer(false)}
            isModal={true}
          />
        )}
      </div>
    </DndContext>
  );
};

export default AgentBuilder;
