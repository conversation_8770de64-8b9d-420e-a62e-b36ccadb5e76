import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Cog6ToothIcon,
  KeyIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface AgentSettingsProps {
  agent?: any;
  onSave: (data: any) => void;
  isLoading: boolean;
  isNewAgent: boolean;
}

const AgentSettings: React.FC<AgentSettingsProps> = ({
  agent,
  onSave,
  isLoading,
  isNewAgent
}) => {
  const [settings, setSettings] = useState({
    // Basic settings
    name: '',
    description: '',
    avatar: '',
    isActive: true,
    isPublic: false,
    
    // Pricing settings
    pricingType: 'FREE',
    price: null,
    currency: 'USD',
    
    // Usage limits
    dailyLimit: null,
    monthlyLimit: null,
    
    // Deployment settings
    deploymentType: 'WIDGET',
    customDomain: '',
    
    // API settings
    apiKey: '',
    
    // Advanced settings
    aiProvider: 'OPENAI',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1000
  });

  const [showApiKey, setShowApiKey] = useState(false);
  const [activeSection, setActiveSection] = useState('basic');

  useEffect(() => {
    if (agent) {
      setSettings({
        name: agent.name || '',
        description: agent.description || '',
        avatar: agent.avatar || '',
        isActive: agent.isActive ?? true,
        isPublic: agent.isPublic ?? false,
        pricingType: agent.pricingType || 'FREE',
        price: agent.price,
        currency: agent.currency || 'USD',
        dailyLimit: agent.dailyLimit,
        monthlyLimit: agent.monthlyLimit,
        deploymentType: agent.deploymentType || 'WIDGET',
        customDomain: agent.customDomain || '',
        apiKey: agent.apiKey || '',
        aiProvider: agent.aiProvider || 'OPENAI',
        model: agent.model || 'gpt-3.5-turbo',
        temperature: agent.temperature || 0.7,
        maxTokens: agent.maxTokens || 1000
      });
    }
  }, [agent]);

  const updateSetting = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onSave(settings);
  };

  const copyApiKey = () => {
    if (settings.apiKey) {
      navigator.clipboard.writeText(settings.apiKey);
      toast.success('API key copied to clipboard');
    }
  };

  const generateNewApiKey = () => {
    // In a real app, this would call an API to generate a new key
    const newKey = 'pk_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    updateSetting('apiKey', newKey);
    toast.success('New API key generated');
  };

  const sections = [
    { id: 'basic', name: 'Basic Settings', icon: Cog6ToothIcon },
    { id: 'pricing', name: 'Pricing & Monetization', icon: CurrencyDollarIcon },
    { id: 'limits', name: 'Usage Limits', icon: ChartBarIcon },
    { id: 'deployment', name: 'Deployment', icon: GlobeAltIcon },
    { id: 'api', name: 'API Settings', icon: KeyIcon },
    { id: 'advanced', name: 'Advanced', icon: ShieldCheckIcon }
  ];

  const renderBasicSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Agent Name *
          </label>
          <input
            type="text"
            value={settings.name}
            onChange={(e) => updateSetting('name', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="My AI Assistant"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Avatar URL
          </label>
          <input
            type="url"
            value={settings.avatar}
            onChange={(e) => updateSetting('avatar', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="https://example.com/avatar.png"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          value={settings.description}
          onChange={(e) => updateSetting('description', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Describe what your agent does..."
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-900">Agent Status</h3>
          <p className="text-sm text-gray-500">Enable or disable your agent</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.isActive}
            onChange={(e) => updateSetting('isActive', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-900">Public Agent</h3>
          <p className="text-sm text-gray-500">Make your agent discoverable in the marketplace</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.isPublic}
            onChange={(e) => updateSetting('isPublic', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderPricingSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Pricing Type
        </label>
        <select
          value={settings.pricingType}
          onChange={(e) => updateSetting('pricingType', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="FREE">Free</option>
          <option value="ONE_TIME">One-time Payment</option>
          <option value="SUBSCRIPTION">Subscription</option>
          <option value="PAY_PER_USE">Pay Per Use</option>
        </select>
      </div>

      {settings.pricingType !== 'FREE' && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price
            </label>
            <input
              type="number"
              value={settings.price || ''}
              onChange={(e) => updateSetting('price', parseFloat(e.target.value) || null)}
              min="0"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="9.99"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency
            </label>
            <select
              value={settings.currency}
              onChange={(e) => updateSetting('currency', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="CAD">CAD</option>
              <option value="AUD">AUD</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );

  const renderApiSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          API Key
        </label>
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <input
              type={showApiKey ? 'text' : 'password'}
              value={settings.apiKey}
              readOnly
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
            />
            <button
              type="button"
              onClick={() => setShowApiKey(!showApiKey)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showApiKey ? (
                <EyeSlashIcon className="h-4 w-4 text-gray-400" />
              ) : (
                <EyeIcon className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          <button
            onClick={copyApiKey}
            className="btn-outline btn-sm"
            disabled={!settings.apiKey}
          >
            Copy
          </button>
          <button
            onClick={generateNewApiKey}
            className="btn-primary btn-sm"
          >
            Regenerate
          </button>
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Use this API key to authenticate requests to your agent
        </p>
      </div>
    </div>
  );

  return (
    <div className="h-full bg-gray-50">
      <div className="container-custom py-6">
        <div className="flex items-center space-x-3 mb-6">
          <Cog6ToothIcon className="h-6 w-6 text-primary-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Agent Settings</h2>
            <p className="text-gray-600">Configure your agent's behavior and deployment options</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <section.icon className="h-5 w-5" />
                  <span className="text-sm font-medium">{section.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2 }}
              >
                {activeSection === 'basic' && renderBasicSettings()}
                {activeSection === 'pricing' && renderPricingSettings()}
                {activeSection === 'api' && renderApiSettings()}
                {/* Add other sections as needed */}
              </motion.div>

              {/* Save Button */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex justify-end">
                  <button
                    onClick={handleSave}
                    disabled={isLoading}
                    className="btn-primary"
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="spinner w-4 h-4"></div>
                        <span>Saving...</span>
                      </div>
                    ) : (
                      'Save Settings'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentSettings;
