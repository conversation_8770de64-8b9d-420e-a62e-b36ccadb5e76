import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  EyeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

interface AgentPreviewProps {
  agent?: any;
  isNewAgent: boolean;
}

const AgentPreview: React.FC<AgentPreviewProps> = ({ agent, isNewAgent }) => {
  const [previewMode, setPreviewMode] = useState<'widget' | 'standalone' | 'api'>('widget');
  const [deviceView, setDeviceView] = useState<'desktop' | 'mobile'>('desktop');

  const previewModes = [
    { id: 'widget', name: 'Widget', icon: CodeBracketIcon, description: 'Embeddable chat widget' },
    { id: 'standalone', name: 'Standalone', icon: GlobeAltIcon, description: 'Full page application' },
    { id: 'api', name: 'API', icon: ShareIcon, description: 'REST API integration' }
  ];

  const generateWidgetCode = () => {
    const agentId = agent?.id || 'your-agent-id';
    return `<!-- PromptCash Widget -->
<div id="promptcash-widget"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://widget.promptcash.com/embed.js';
    script.async = true;
    script.onload = function() {
      PromptCash.init({
        agentId: '${agentId}',
        theme: 'light',
        position: 'bottom-right',
        primaryColor: '#3B82F6'
      });
    };
    document.head.appendChild(script);
  })();
</script>`;
  };

  const generateAPIExample = () => {
    const agentId = agent?.id || 'your-agent-id';
    const apiKey = agent?.apiKey || 'your-api-key';
    
    return `curl -X POST https://api.promptcash.com/v1/agents/${agentId}/chat \\
  -H "Authorization: Bearer ${apiKey}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Hello, how can you help me?",
    "context": []
  }'`;
  };

  const renderWidgetPreview = () => (
    <div className={`bg-gray-100 rounded-lg p-4 ${
      deviceView === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
    }`}>
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Widget Header */}
        <div className="bg-primary-600 text-white p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-medium">{agent?.name || 'AI Assistant'}</h3>
              <p className="text-sm text-primary-100">Online</p>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="p-4 space-y-3 h-64 overflow-y-auto">
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-3 py-2 max-w-xs">
              <p className="text-sm">Hello! I'm {agent?.name || 'your AI assistant'}. How can I help you today?</p>
            </div>
          </div>
          <div className="flex justify-end">
            <div className="bg-primary-600 text-white rounded-lg px-3 py-2 max-w-xs">
              <p className="text-sm">What can you do?</p>
            </div>
          </div>
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-3 py-2 max-w-xs">
              <p className="text-sm">
                {agent?.description || 'I can help you with various tasks based on my configuration. Feel free to ask me anything!'}
              </p>
            </div>
          </div>
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex space-x-2">
            <input
              type="text"
              placeholder="Type your message..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              disabled
            />
            <button className="btn-primary px-3 py-2 text-sm" disabled>
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStandalonePreview = () => (
    <div className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${
      deviceView === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
    }`}>
      {/* App Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">AI</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{agent?.name || 'AI Assistant'}</h1>
              <p className="text-sm text-gray-500">{agent?.description || 'Your AI-powered assistant'}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-500">Online</span>
          </div>
        </div>
      </div>

      {/* Chat Area */}
      <div className="h-96 flex flex-col">
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-primary-600">AI</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Welcome to {agent?.name || 'AI Assistant'}
            </h2>
            <p className="text-gray-600">
              {agent?.description || 'Start a conversation to see how I can help you.'}
            </p>
          </div>
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex space-x-3">
            <input
              type="text"
              placeholder="Type your message..."
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              disabled
            />
            <button className="btn-primary px-6 py-3" disabled>
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAPIPreview = () => (
    <div className="space-y-6">
      {/* API Endpoint */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">API Endpoint</h3>
        <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
          <div className="flex items-center space-x-2 mb-2">
            <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">POST</span>
            <span>https://api.promptcash.com/v1/agents/{agent?.id || 'your-agent-id'}/chat</span>
          </div>
        </div>
      </div>

      {/* cURL Example */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">cURL Example</h3>
        <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm overflow-x-auto">
          <pre>{generateAPIExample()}</pre>
        </div>
      </div>

      {/* Response Example */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Response Example</h3>
        <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
          <pre>{`{
  "success": true,
  "data": {
    "message": "Hello! I'm ${agent?.name || 'your AI assistant'}. How can I help you today?",
    "tokens": 25,
    "cost": 0.0001,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}`}</pre>
        </div>
      </div>

      {/* Authentication */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Authentication</h3>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            Include your API key in the Authorization header: <code className="bg-blue-100 px-1 rounded">Bearer {agent?.apiKey || 'your-api-key'}</code>
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full bg-gray-50">
      <div className="container-custom py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <EyeIcon className="h-6 w-6 text-primary-600" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Preview</h2>
              <p className="text-gray-600">See how your agent will look and work</p>
            </div>
          </div>

          {/* Device Toggle */}
          {(previewMode === 'widget' || previewMode === 'standalone') && (
            <div className="flex items-center space-x-2 bg-white rounded-lg p-1 border border-gray-200">
              <button
                onClick={() => setDeviceView('desktop')}
                className={`p-2 rounded ${
                  deviceView === 'desktop'
                    ? 'bg-primary-100 text-primary-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <ComputerDesktopIcon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setDeviceView('mobile')}
                className={`p-2 rounded ${
                  deviceView === 'mobile'
                    ? 'bg-primary-100 text-primary-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <DevicePhoneMobileIcon className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>

        {/* Preview Mode Tabs */}
        <div className="flex space-x-1 bg-white rounded-lg p-1 border border-gray-200 mb-6">
          {previewModes.map((mode) => (
            <button
              key={mode.id}
              onClick={() => setPreviewMode(mode.id as any)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
                previewMode === mode.id
                  ? 'bg-primary-100 text-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <mode.icon className="h-5 w-5" />
              <span className="font-medium">{mode.name}</span>
            </button>
          ))}
        </div>

        {/* Preview Content */}
        <motion.div
          key={previewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {previewMode === 'widget' && renderWidgetPreview()}
          {previewMode === 'standalone' && renderStandalonePreview()}
          {previewMode === 'api' && renderAPIPreview()}
        </motion.div>

        {/* Widget Code */}
        {previewMode === 'widget' && (
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Embed Code</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm overflow-x-auto">
              <pre>{generateWidgetCode()}</pre>
            </div>
            <p className="mt-2 text-sm text-gray-600">
              Copy and paste this code into your website to embed the chat widget.
            </p>
          </div>
        )}

        {/* Notice for new agents */}
        {isNewAgent && (
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <EyeIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-yellow-800">Preview Mode</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  This is a preview of how your agent will look. Save your agent to enable full functionality and get your API keys.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentPreview;
