const { getPrismaClient } = require('../config/database');
const { cache } = require('../config/redis');
const aiService = require('../services/aiService');
const { 
  validatePromptLength, 
  validateAgentName, 
  validateTemperature, 
  validateMaxTokens,
  validatePrice,
  validateCurrency 
} = require('../utils/validation');
const crypto = require('crypto');

const prisma = getPrismaClient();

// Get all agents for a user
const getAgents = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const offset = (page - 1) * limit;

    const where = {
      userId: req.user.id,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(status && { isActive: status === 'active' })
    };

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(offset),
        take: parseInt(limit),
        orderBy: { [sortBy]: sortOrder },
        include: {
          _count: {
            select: {
              interactions: true,
              subscriptions: true
            }
          }
        }
      }),
      prisma.agent.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        agents,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get agents error:', error);
    res.status(500).json({
      error: 'Failed to fetch agents'
    });
  }
};

// Get single agent
const getAgent = async (req, res) => {
  try {
    const { id } = req.params;

    const agent = await prisma.agent.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            interactions: true,
            subscriptions: true
          }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({
        error: 'Agent not found'
      });
    }

    // Check ownership
    if (agent.userId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { agent }
    });
  } catch (error) {
    console.error('Get agent error:', error);
    res.status(500).json({
      error: 'Failed to fetch agent'
    });
  }
};

// Create new agent
const createAgent = async (req, res) => {
  try {
    const {
      name,
      description,
      prompt,
      personality,
      responseTemplates,
      avatar,
      pricingType = 'FREE',
      price,
      currency = 'USD',
      aiProvider = 'OPENAI',
      model = 'gpt-3.5-turbo',
      temperature = 0.7,
      maxTokens = 1000,
      dailyLimit,
      monthlyLimit
    } = req.body;

    // Validation
    if (!validateAgentName(name)) {
      return res.status(400).json({
        error: 'Agent name must be between 2 and 100 characters'
      });
    }

    if (!validatePromptLength(prompt)) {
      return res.status(400).json({
        error: 'Prompt must be between 10 and 10,000 characters'
      });
    }

    if (!validateTemperature(temperature)) {
      return res.status(400).json({
        error: 'Temperature must be between 0 and 2'
      });
    }

    if (!validateMaxTokens(maxTokens)) {
      return res.status(400).json({
        error: 'Max tokens must be between 1 and 4,000'
      });
    }

    if (price && !validatePrice(price)) {
      return res.status(400).json({
        error: 'Invalid price format'
      });
    }

    if (currency && !validateCurrency(currency)) {
      return res.status(400).json({
        error: 'Invalid currency code'
      });
    }

    // Generate API key for the agent
    const apiKey = crypto.randomBytes(32).toString('hex');

    const agent = await prisma.agent.create({
      data: {
        name,
        description,
        prompt,
        personality: personality || {},
        responseTemplates: responseTemplates || [],
        avatar,
        pricingType,
        price: price ? parseFloat(price) : null,
        currency,
        aiProvider,
        model,
        temperature: parseFloat(temperature),
        maxTokens: parseInt(maxTokens),
        dailyLimit: dailyLimit ? parseInt(dailyLimit) : null,
        monthlyLimit: monthlyLimit ? parseInt(monthlyLimit) : null,
        apiKey,
        userId: req.user.id
      }
    });

    res.status(201).json({
      success: true,
      message: 'Agent created successfully',
      data: { agent }
    });
  } catch (error) {
    console.error('Create agent error:', error);
    res.status(500).json({
      error: 'Failed to create agent'
    });
  }
};

// Update agent
const updateAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body };

    // Remove fields that shouldn't be updated directly
    delete updateData.id;
    delete updateData.userId;
    delete updateData.apiKey;
    delete updateData.createdAt;

    // Validation
    if (updateData.name && !validateAgentName(updateData.name)) {
      return res.status(400).json({
        error: 'Agent name must be between 2 and 100 characters'
      });
    }

    if (updateData.prompt && !validatePromptLength(updateData.prompt)) {
      return res.status(400).json({
        error: 'Prompt must be between 10 and 10,000 characters'
      });
    }

    if (updateData.temperature && !validateTemperature(updateData.temperature)) {
      return res.status(400).json({
        error: 'Temperature must be between 0 and 2'
      });
    }

    if (updateData.maxTokens && !validateMaxTokens(updateData.maxTokens)) {
      return res.status(400).json({
        error: 'Max tokens must be between 1 and 4,000'
      });
    }

    if (updateData.price && !validatePrice(updateData.price)) {
      return res.status(400).json({
        error: 'Invalid price format'
      });
    }

    // Convert numeric fields
    if (updateData.temperature) updateData.temperature = parseFloat(updateData.temperature);
    if (updateData.maxTokens) updateData.maxTokens = parseInt(updateData.maxTokens);
    if (updateData.price) updateData.price = parseFloat(updateData.price);
    if (updateData.dailyLimit) updateData.dailyLimit = parseInt(updateData.dailyLimit);
    if (updateData.monthlyLimit) updateData.monthlyLimit = parseInt(updateData.monthlyLimit);

    const agent = await prisma.agent.update({
      where: { id },
      data: updateData
    });

    // Clear cache for this agent
    await cache.del(`agent:${id}`);

    res.json({
      success: true,
      message: 'Agent updated successfully',
      data: { agent }
    });
  } catch (error) {
    console.error('Update agent error:', error);
    res.status(500).json({
      error: 'Failed to update agent'
    });
  }
};

// Delete agent
const deleteAgent = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.agent.delete({
      where: { id }
    });

    // Clear cache
    await cache.del(`agent:${id}`);

    res.json({
      success: true,
      message: 'Agent deleted successfully'
    });
  } catch (error) {
    console.error('Delete agent error:', error);
    res.status(500).json({
      error: 'Failed to delete agent'
    });
  }
};

// Test agent with input
const testAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const { input, context } = req.body;

    if (!input || input.trim().length === 0) {
      return res.status(400).json({
        error: 'Input is required for testing'
      });
    }

    const agent = await prisma.agent.findUnique({
      where: { id }
    });

    if (!agent) {
      return res.status(404).json({
        error: 'Agent not found'
      });
    }

    // Generate response using AI service
    const response = await aiService.generateResponse({
      prompt: agent.prompt,
      input,
      personality: agent.personality,
      provider: agent.aiProvider,
      model: agent.model,
      temperature: agent.temperature,
      maxTokens: agent.maxTokens,
      context
    });

    res.json({
      success: true,
      data: {
        input,
        output: response.text,
        tokens: response.tokens,
        cost: response.cost
      }
    });
  } catch (error) {
    console.error('Test agent error:', error);
    res.status(500).json({
      error: 'Failed to test agent'
    });
  }
};

// Clone agent
const cloneAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    const originalAgent = await prisma.agent.findUnique({
      where: { id }
    });

    if (!originalAgent) {
      return res.status(404).json({
        error: 'Agent not found'
      });
    }

    // Generate new API key
    const apiKey = crypto.randomBytes(32).toString('hex');

    const clonedAgent = await prisma.agent.create({
      data: {
        name: name || `${originalAgent.name} (Copy)`,
        description: originalAgent.description,
        prompt: originalAgent.prompt,
        personality: originalAgent.personality,
        responseTemplates: originalAgent.responseTemplates,
        avatar: originalAgent.avatar,
        pricingType: originalAgent.pricingType,
        price: originalAgent.price,
        currency: originalAgent.currency,
        aiProvider: originalAgent.aiProvider,
        model: originalAgent.model,
        temperature: originalAgent.temperature,
        maxTokens: originalAgent.maxTokens,
        dailyLimit: originalAgent.dailyLimit,
        monthlyLimit: originalAgent.monthlyLimit,
        apiKey,
        userId: req.user.id,
        isActive: false // Start as inactive
      }
    });

    res.status(201).json({
      success: true,
      message: 'Agent cloned successfully',
      data: { agent: clonedAgent }
    });
  } catch (error) {
    console.error('Clone agent error:', error);
    res.status(500).json({
      error: 'Failed to clone agent'
    });
  }
};

// Toggle agent status
const toggleAgentStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const agent = await prisma.agent.findUnique({
      where: { id },
      select: { isActive: true }
    });

    if (!agent) {
      return res.status(404).json({
        error: 'Agent not found'
      });
    }

    const updatedAgent = await prisma.agent.update({
      where: { id },
      data: { isActive: !agent.isActive }
    });

    // Clear cache
    await cache.del(`agent:${id}`);

    res.json({
      success: true,
      message: `Agent ${updatedAgent.isActive ? 'activated' : 'deactivated'} successfully`,
      data: { agent: updatedAgent }
    });
  } catch (error) {
    console.error('Toggle agent status error:', error);
    res.status(500).json({
      error: 'Failed to toggle agent status'
    });
  }
};

module.exports = {
  getAgents,
  getAgent,
  createAgent,
  updateAgent,
  deleteAgent,
  testAgent,
  cloneAgent,
  toggleAgentStatus
};
