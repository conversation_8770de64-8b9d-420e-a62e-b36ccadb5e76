import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Create axios instance
export const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // Log requests in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log responses in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    return response;
  },
  (error) => {
    // Log errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }

    // Handle network errors
    if (!error.response) {
      toast.error('Network error. Please check your connection.');
      return Promise.reject(error);
    }

    // Handle specific error codes
    const { status, data } = error.response;

    switch (status) {
      case 400:
        // Bad request - usually validation errors
        if (data.errors && Array.isArray(data.errors)) {
          data.errors.forEach((err: string) => toast.error(err));
        } else if (data.error) {
          toast.error(data.error);
        }
        break;

      case 401:
        // Unauthorized - handled by auth context
        break;

      case 403:
        // Forbidden
        toast.error('Access denied. You do not have permission to perform this action.');
        break;

      case 404:
        // Not found
        toast.error('Resource not found.');
        break;

      case 409:
        // Conflict
        toast.error(data.error || 'Conflict occurred.');
        break;

      case 422:
        // Unprocessable entity - validation errors
        if (data.errors) {
          Object.values(data.errors).forEach((err: any) => {
            if (Array.isArray(err)) {
              err.forEach((e: string) => toast.error(e));
            } else {
              toast.error(err);
            }
          });
        }
        break;

      case 429:
        // Rate limit exceeded
        toast.error('Too many requests. Please try again later.');
        break;

      case 500:
        // Internal server error
        toast.error('Server error. Please try again later.');
        break;

      case 502:
      case 503:
      case 504:
        // Server unavailable
        toast.error('Service temporarily unavailable. Please try again later.');
        break;

      default:
        toast.error('An unexpected error occurred.');
    }

    return Promise.reject(error);
  }
);

// API helper functions
export const apiHelpers = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.get<T>(url, config);
    return response.data;
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.post<T>(url, data, config);
    return response.data;
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.put<T>(url, data, config);
    return response.data;
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.patch<T>(url, data, config);
    return response.data;
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.delete<T>(url, config);
    return response.data;
  },

  // Upload file
  upload: async <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  },

  // Download file
  download: async (url: string, filename?: string): Promise<void> => {
    const response = await api.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

// Specific API endpoints
export const authAPI = {
  login: (email: string, password: string, rememberMe?: boolean) =>
    apiHelpers.post('/auth/login', { email, password, rememberMe }),

  register: (data: {
    email: string;
    username: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) => apiHelpers.post('/auth/register', data),

  logout: (refreshToken: string) =>
    apiHelpers.post('/auth/logout', { refreshToken }),

  refreshToken: (refreshToken: string) =>
    apiHelpers.post('/auth/refresh', { refreshToken }),

  getProfile: () => apiHelpers.get('/auth/profile'),

  updateProfile: (data: any) => apiHelpers.put('/auth/profile', data),

  requestPasswordReset: (email: string) =>
    apiHelpers.post('/auth/request-password-reset', { email }),

  resetPassword: (token: string, password: string) =>
    apiHelpers.post('/auth/reset-password', { token, password }),

  verifyEmail: (token: string) => apiHelpers.get(`/auth/verify-email/${token}`),
};

export const agentsAPI = {
  getAll: (params?: any) => apiHelpers.get('/agents', { params }),
  
  getById: (id: string) => apiHelpers.get(`/agents/${id}`),
  
  create: (data: any) => apiHelpers.post('/agents', data),
  
  update: (id: string, data: any) => apiHelpers.put(`/agents/${id}`, data),
  
  delete: (id: string) => apiHelpers.delete(`/agents/${id}`),
  
  deploy: (id: string, type: string) => apiHelpers.post(`/agents/${id}/deploy`, { type }),
  
  test: (id: string, input: string) => apiHelpers.post(`/agents/${id}/test`, { input }),
  
  getAnalytics: (id: string, params?: any) => apiHelpers.get(`/agents/${id}/analytics`, { params }),
};

export const paymentsAPI = {
  getPlans: () => apiHelpers.get('/payments/plans'),
  
  createSubscription: (planId: string, paymentMethodId: string) =>
    apiHelpers.post('/payments/subscriptions', { planId, paymentMethodId }),
  
  cancelSubscription: (subscriptionId: string) =>
    apiHelpers.delete(`/payments/subscriptions/${subscriptionId}`),
  
  getPaymentHistory: (params?: any) => apiHelpers.get('/payments/history', { params }),
  
  createPaymentIntent: (amount: number, currency: string) =>
    apiHelpers.post('/payments/intents', { amount, currency }),
  
  getInvoices: (params?: any) => apiHelpers.get('/payments/invoices', { params }),
};

export const analyticsAPI = {
  getDashboard: (params?: any) => apiHelpers.get('/analytics/dashboard', { params }),
  
  getRevenue: (params?: any) => apiHelpers.get('/analytics/revenue', { params }),
  
  getUsage: (params?: any) => apiHelpers.get('/analytics/usage', { params }),
  
  getUsers: (params?: any) => apiHelpers.get('/analytics/users', { params }),
  
  export: (type: string, params?: any) => apiHelpers.get(`/analytics/export/${type}`, { params }),
};

// Utility functions
export const setAuthToken = (token: string) => {
  api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
};

export const removeAuthToken = () => {
  delete api.defaults.headers.common['Authorization'];
};

export const isApiError = (error: any): boolean => {
  return error.response && error.response.data;
};

export const getApiErrorMessage = (error: any): string => {
  if (isApiError(error)) {
    return error.response.data.error || error.response.data.message || 'An error occurred';
  }
  return error.message || 'An unexpected error occurred';
};

export default api;
