const express = require('express');
const { body, param, query } = require('express-validator');
const rateLimit = require('express-rate-limit');
const { requireAgentOwnership, userRateLimit } = require('../middleware/auth');
const {
  getAgents,
  getAgent,
  createAgent,
  updateAgent,
  deleteAgent,
  testAgent,
  cloneAgent,
  toggleAgentStatus
} = require('../controllers/agentController');

const router = express.Router();

// Rate limiting for agent operations
const agentLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // 50 requests per window
  message: {
    error: 'Too many agent requests, please try again later'
  }
});

const testLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 tests per minute
  message: {
    error: 'Too many test requests, please try again later'
  }
});

// Validation middleware
const createAgentValidation = [
  body('name')
    .isLength({ min: 2, max: 100 })
    .withMessage('Agent name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-_.,!?]+$/)
    .withMessage('Agent name contains invalid characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('prompt')
    .isLength({ min: 10, max: 10000 })
    .withMessage('Prompt must be between 10 and 10,000 characters'),
  body('personality')
    .optional()
    .isObject()
    .withMessage('Personality must be an object'),
  body('responseTemplates')
    .optional()
    .isArray()
    .withMessage('Response templates must be an array'),
  body('pricingType')
    .optional()
    .isIn(['FREE', 'ONE_TIME', 'SUBSCRIPTION', 'PAY_PER_USE'])
    .withMessage('Invalid pricing type'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('currency')
    .optional()
    .isIn(['USD', 'EUR', 'GBP', 'CAD', 'AUD'])
    .withMessage('Invalid currency'),
  body('aiProvider')
    .optional()
    .isIn(['OPENAI', 'ANTHROPIC'])
    .withMessage('Invalid AI provider'),
  body('temperature')
    .optional()
    .isFloat({ min: 0, max: 2 })
    .withMessage('Temperature must be between 0 and 2'),
  body('maxTokens')
    .optional()
    .isInt({ min: 1, max: 4000 })
    .withMessage('Max tokens must be between 1 and 4,000'),
  body('dailyLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Daily limit must be a positive integer'),
  body('monthlyLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Monthly limit must be a positive integer')
];

const updateAgentValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid agent ID'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Agent name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('prompt')
    .optional()
    .isLength({ min: 10, max: 10000 })
    .withMessage('Prompt must be between 10 and 10,000 characters'),
  body('temperature')
    .optional()
    .isFloat({ min: 0, max: 2 })
    .withMessage('Temperature must be between 0 and 2'),
  body('maxTokens')
    .optional()
    .isInt({ min: 1, max: 4000 })
    .withMessage('Max tokens must be between 1 and 4,000')
];

const testAgentValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid agent ID'),
  body('input')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Input must be between 1 and 1,000 characters'),
  body('context')
    .optional()
    .isArray()
    .withMessage('Context must be an array')
];

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Search query must be less than 100 characters'),
  query('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('Status must be active or inactive'),
  query('sortBy')
    .optional()
    .isIn(['name', 'createdAt', 'updatedAt'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc')
];

/**
 * @swagger
 * components:
 *   schemas:
 *     Agent:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         prompt:
 *           type: string
 *         personality:
 *           type: object
 *         responseTemplates:
 *           type: array
 *         isActive:
 *           type: boolean
 *         pricingType:
 *           type: string
 *           enum: [FREE, ONE_TIME, SUBSCRIPTION, PAY_PER_USE]
 *         price:
 *           type: number
 *         currency:
 *           type: string
 *         aiProvider:
 *           type: string
 *           enum: [OPENAI, ANTHROPIC]
 *         model:
 *           type: string
 *         temperature:
 *           type: number
 *         maxTokens:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/agents:
 *   get:
 *     summary: Get all agents for the authenticated user
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: List of agents retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     agents:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Agent'
 *                     pagination:
 *                       type: object
 */
router.get('/', agentLimiter, userRateLimit(), queryValidation, getAgents);

/**
 * @swagger
 * /api/agents/{id}:
 *   get:
 *     summary: Get a specific agent
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     agent:
 *                       $ref: '#/components/schemas/Agent'
 */
router.get('/:id', agentLimiter, requireAgentOwnership, getAgent);

/**
 * @swagger
 * /api/agents:
 *   post:
 *     summary: Create a new agent
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - prompt
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               prompt:
 *                 type: string
 *               personality:
 *                 type: object
 *               responseTemplates:
 *                 type: array
 *               pricingType:
 *                 type: string
 *               price:
 *                 type: number
 *               currency:
 *                 type: string
 *     responses:
 *       201:
 *         description: Agent created successfully
 */
router.post('/', agentLimiter, userRateLimit(), createAgentValidation, createAgent);

/**
 * @swagger
 * /api/agents/{id}:
 *   put:
 *     summary: Update an agent
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Agent updated successfully
 */
router.put('/:id', agentLimiter, requireAgentOwnership, updateAgentValidation, updateAgent);

/**
 * @swagger
 * /api/agents/{id}:
 *   delete:
 *     summary: Delete an agent
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Agent deleted successfully
 */
router.delete('/:id', agentLimiter, requireAgentOwnership, deleteAgent);

/**
 * @swagger
 * /api/agents/{id}/test:
 *   post:
 *     summary: Test an agent with input
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - input
 *             properties:
 *               input:
 *                 type: string
 *               context:
 *                 type: array
 *     responses:
 *       200:
 *         description: Agent test completed successfully
 */
router.post('/:id/test', testLimiter, requireAgentOwnership, testAgentValidation, testAgent);

/**
 * @swagger
 * /api/agents/{id}/clone:
 *   post:
 *     summary: Clone an agent
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Agent cloned successfully
 */
router.post('/:id/clone', agentLimiter, requireAgentOwnership, cloneAgent);

/**
 * @swagger
 * /api/agents/{id}/toggle:
 *   patch:
 *     summary: Toggle agent active status
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Agent status toggled successfully
 */
router.patch('/:id/toggle', agentLimiter, requireAgentOwnership, toggleAgentStatus);

module.exports = router;
