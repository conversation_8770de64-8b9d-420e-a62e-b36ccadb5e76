import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Meta tags */}
        <meta charSet="utf-8" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="description" content="PromptCash - Build, deploy, and monetize AI agents from custom prompts" />
        <meta name="keywords" content="AI, agents, chatbots, monetization, prompts, SaaS, platform" />
        <meta name="author" content="PromptCash" />
        
        {/* Open Graph */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="PromptCash - Monetize Your AI Interactions" />
        <meta property="og:description" content="Build, deploy, and monetize AI agents from custom prompts with our comprehensive platform" />
        <meta property="og:image" content="/images/og-image.png" />
        <meta property="og:url" content="https://promptcash.com" />
        <meta property="og:site_name" content="PromptCash" />
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="PromptCash - Monetize Your AI Interactions" />
        <meta name="twitter:description" content="Build, deploy, and monetize AI agents from custom prompts" />
        <meta name="twitter:image" content="/images/twitter-card.png" />
        <meta name="twitter:creator" content="@promptcash" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://api.stripe.com" />
        
        {/* DNS prefetch */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//api.stripe.com" />
        
        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}', {
                    page_title: document.title,
                    page_location: window.location.href,
                  });
                `,
              }}
            />
          </>
        )}
        
        {/* Sentry */}
        {process.env.NEXT_PUBLIC_SENTRY_DSN && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                import * as Sentry from "@sentry/nextjs";
                Sentry.init({
                  dsn: "${process.env.NEXT_PUBLIC_SENTRY_DSN}",
                  environment: "${process.env.NODE_ENV}",
                });
              `,
            }}
          />
        )}
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'SoftwareApplication',
              name: 'PromptCash',
              description: 'Build, deploy, and monetize AI agents from custom prompts',
              url: 'https://promptcash.com',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web',
              offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD',
              },
              author: {
                '@type': 'Organization',
                name: 'PromptCash',
                url: 'https://promptcash.com',
              },
            }),
          }}
        />
      </Head>
      <body>
        {/* No-script fallback */}
        <noscript>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            fontSize: '18px',
            textAlign: 'center',
            padding: '20px'
          }}>
            <div>
              <h1>JavaScript Required</h1>
              <p>PromptCash requires JavaScript to function properly. Please enable JavaScript in your browser settings.</p>
            </div>
          </div>
        </noscript>
        
        <Main />
        <NextScript />
        
        {/* Loading indicator for slow connections */}
        <div id="loading-indicator" style={{ display: 'none' }}>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9998
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}></div>
          </div>
        </div>
        
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </body>
    </Html>
  );
}
